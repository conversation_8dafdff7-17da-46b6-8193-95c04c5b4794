/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

function getCookie(name) {
    var cookie = {};
    document.cookie.split(';').forEach(function (el) {
        var result = el.split('=');
        var k = result[0];
        var v = result[1];
        cookie[k.trim()] = v;
    })
    return cookie[name];
}
(function () {

    'use strict';


    angular.module('scmApp').factory('apiJson', APIJson);
    function APIJson() {
        var stageUrl = "http://stage.kettle.chaayos.com:8080/";
        var masterBaseUrl = stageUrl;//window.location.origin+"/";//window.masterUrl;
        var scmBaseUrl = "http://localhost:9898/";//window.location.origin+"/";//window.scmUrl;
        var kettleUrl = window.location.origin+"/";//window.kettleUrl;
        var crmUrl = window.location.origin+"/";//window.crmUrl;
        var analyticsUrl = window.location.origin+"/";//window.analyticsUrl;
        //var imageBaseUrl = window.imageUrl;

        var imageBaseUrl;
        if (window.location.href.indexOf("orient") >= 0) {
            imageBaseUrl = "https://d8xnaajozedwc.cloudfront.net/";
        } else if (window.location.href.indexOf("internal") >= 0) {
            imageBaseUrl = "https://d8xnaajozedwc.cloudfront.net/";
        } else if (window.location.href.indexOf("relax") >= 0) {
            imageBaseUrl = "https://d8xnaajozedwc.cloudfront.net/";
        }else if (window.location.href.indexOf("sumo") >= 0) {
            imageBaseUrl = "https://d8xnaajozedwc.cloudfront.net/";
        } else if (window.location.href.indexOf("uat.kettle.chaayos") >= 0 || window.location.href.indexOf("dev.kettle.chaayos") >= 0
        || window.location.href.indexOf("stage.kettle.chaayos") >= 0 || window.location.href.indexOf("localhost") >= 0) {
            imageBaseUrl = "http://d3mikkh4u78xq4.cloudfront.net/";
        } else {
            imageBaseUrl = "https://d8xnaajozedwc.cloudfront.net/";
        }

        var productBaseUrl = imageBaseUrl + "product_image/";
        var profileBaseUrl = imageBaseUrl + "profile_attribute_image/";
        var skuBaseUrl = imageBaseUrl + "sku_image/";

        var KETTLE_SERVICE = kettleUrl + "kettle-service/rest/v1/";

        var MASTER_SERVICE = masterBaseUrl + "master-service/rest/v1/";
        var CUSTOMER_SERVICE = crmUrl + "kettle-crm/rest/v1/";
        var SCM_SERVICE = scmBaseUrl + "scm-service/rest/v1/";

        //master root contexts
        var UNIT_METADATA_ROOT_CONTEXT = MASTER_SERVICE + "unit-metadata/";
        var USER_SERVICES_ROOT_CONTEXT = MASTER_SERVICE + "users/";
        var RECIPE_SERVICES_ROOT_CONTEXT = MASTER_SERVICE + "recipe/";
        var ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT = MASTER_SERVICE + "access-control-management/";
        var TAX_METADATA_ROOT_CONTEXT = MASTER_SERVICE +"tax-metadata/";
        var MASTER_UNIT_MANAGEMENT  = MASTER_SERVICE + "user-management/";

        //scm root contexts
        var SCM_ROOT_CONTEXT = SCM_SERVICE + "cache-management/";
        var ATTRIBUTE_ROOT_CONTEXT = SCM_SERVICE + "attribute-management/";
        var PROFILE_MANAGEMENT_CONTEXT = SCM_SERVICE + "profile-management/";
        var ASSET_MANAGEMENT_CONTEXT = SCM_SERVICE + "asset-management/";
        var SCM_METADATA_ROOT_CONTEXT = SCM_SERVICE + "scm-metadata/";
        var UNIT_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "unit-management/";
        var PRODUCT_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "product-management/";
        var CATEGORY_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "category-management/";
        var REFERENCE_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "reference-order-management/";
        var REQUEST_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "request-order-management/";
        var TRANSFER_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "transfer-order-management/";
        var PURCHASE_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "purchase-order-management/";
        var GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "goods-receive-management/";
        var VEHICLE_MANAGEMENT = SCM_SERVICE + "vehicle-management/";
        var SCM_UNIT_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "unit-management/";
        var STOCK_MANAGEMENT = SCM_SERVICE + "stock-management/";
        var VENDOR_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "vendor-management/";
        var SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "sku-mapping-management/";
        var WH_STOCK_MANAGEMENT_CONTEXT = SCM_SERVICE + "wh-stock-management/";
        var PRODUCTION_BOOKING_ROOT_CONTEXT = SCM_SERVICE + "production-booking/";
        var PRICE_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "price-management/";
        var PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "payment-request-management/";
        var GENERIC_RESOURCE_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "generic-resource-management/";
        var TRANSPORT_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "transport-management/";
        var INVOICE_MANAGEMENT_CONTEXT = SCM_SERVICE + "invoice-management/";
        var DATA_FILTER_ROOT_CONTEXT = SCM_SERVICE + "filter/";
        var GATEPASS_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "gatepass-management/";
        var SERVICE_MANAGEMENT_ROOT_CONTEXT =  SCM_SERVICE + "service-order-management/";
        var SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "service-receive-management/";
        var SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "service-mapping-management/";
        var CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "capex-management/";
        var PRODUCT_PROJECTIONS_ROOT_CONTEXT = SCM_SERVICE + "product-projections/"
        //Kettle root context
        var KETTLE_METADATA_MANAGEMENT_ROOT_CONTEXT = KETTLE_SERVICE + "metadata-management/";
        var KETTLE_POS_METADATA_ROOT_CONTEXT = KETTLE_SERVICE + "pos-metadata/";
        var VENDOR_REGISTRATION_ROOT_CONTEXT = SCM_SERVICE + "vendor-registration-management/";
        var AUTOMATED_SCM_REPORTS_ROOT_CONTEXT = SCM_SERVICE + "automated-scm-reports-management/";
        var VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "vendor-contract-management/";
        var USER_MANAGEMENT_SERVICES_ROOT_CONTEXT = MASTER_SERVICE + "user-management/";
        var ATTRIBUTE_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "attribute-management"
        var LDC_VENDOR_ROOT_CONTEXT = SCM_SERVICE + "ldc-vendor/";
        var service = {};

        service.productBaseUrl = productBaseUrl;
        service.profileBaseUrl = profileBaseUrl;
        service.skuBaseUrl = skuBaseUrl;

        service.urls = {
        	scmCacheManagement:{
        		scmCacheRefresh: SCM_ROOT_CONTEXT + "refresh-complete-cache",
        		markMilkBreadCompleteForUnit: SCM_ROOT_CONTEXT + "mark-milk-bread-complete-for-unit",
            },
            attributeManagement: {
                definition: ATTRIBUTE_ROOT_CONTEXT + "attribute-definition",
                definitions: ATTRIBUTE_ROOT_CONTEXT + "attribute-definitions",
                value: ATTRIBUTE_ROOT_CONTEXT + "attribute-value",
                values: ATTRIBUTE_ROOT_CONTEXT + "attribute-values",
                addAttribute : ATTRIBUTE_ROOT_CONTEXT + "add/attribute-definition",
                updateAttribute : ATTRIBUTE_ROOT_CONTEXT + "update/attribute-definition"
            },
            scmMetadata: {
                metadata: SCM_METADATA_ROOT_CONTEXT + "metadata",
                uomMetadata: SCM_METADATA_ROOT_CONTEXT + "uomMetadata",
                deliveryLocations: SCM_METADATA_ROOT_CONTEXT + "delivery-locations",
                activeDeliveryLocations: SCM_METADATA_ROOT_CONTEXT + "active-delivery-locations",
                activeStates: SCM_METADATA_ROOT_CONTEXT + "active-states",
                states: SCM_METADATA_ROOT_CONTEXT + "states",
                inventoryList: SCM_METADATA_ROOT_CONTEXT + "inventory-lists",
                companyList: SCM_METADATA_ROOT_CONTEXT + "companies",
                brandList: SCM_METADATA_ROOT_CONTEXT + "brands",
                downloadExcel : SCM_METADATA_ROOT_CONTEXT + "get-Excell",
                getUnitMapDistance : SCM_METADATA_ROOT_CONTEXT + "get-unit-distance",
                validateForDeactivation : SCM_METADATA_ROOT_CONTEXT + "validate-for-deactivation",
                maintenanceWhMapping : SCM_METADATA_ROOT_CONTEXT + "maintenance-wh-mapping",
                dumpAuditData : SCM_METADATA_ROOT_CONTEXT + "dump-audit-data",
                getAllBrands : SCM_METADATA_ROOT_CONTEXT + "get-brands",
                getAllCompanies : SCM_METADATA_ROOT_CONTEXT + "get-companies",
                getDepartments : SCM_METADATA_ROOT_CONTEXT + "get-departments",
            },
            profileManagement: {
                profileDefinition : PROFILE_MANAGEMENT_CONTEXT + "profiles",
                addProfileDefinition : PROFILE_MANAGEMENT_CONTEXT + "profile",
                profileAttributeMapping : PROFILE_MANAGEMENT_CONTEXT + "profile-attributes-mappings",
                addProfileAttributeMapping : PROFILE_MANAGEMENT_CONTEXT + "profile-attributes-mappings",
                uploadProfileAttributeMappingImg : PROFILE_MANAGEMENT_CONTEXT + "upload-profile-attribute-image",
                getProfile : PROFILE_MANAGEMENT_CONTEXT + "profile",
                profileAttributeMappingSKU : PROFILE_MANAGEMENT_CONTEXT + "profile-attributes-mappings-sku",
                profileAttributeMappingAsset : PROFILE_MANAGEMENT_CONTEXT + "profile-attributes-mappings-asset",
                getEntityAttributeValueMappings : PROFILE_MANAGEMENT_CONTEXT + "generic-attribute-value-mappings"
            },
            assetManagement : {
                addAssets :   ASSET_MANAGEMENT_CONTEXT + "assets",
                completeAssetGeneration : ASSET_MANAGEMENT_CONTEXT + "asset-completion",
                getAssetWithUnit : ASSET_MANAGEMENT_CONTEXT + "asset-unit",
                getAllAsset : ASSET_MANAGEMENT_CONTEXT + "asset-all",
                getAssetSlimWithUnit : ASSET_MANAGEMENT_CONTEXT + "asset-unit-slim",
                getAssetNameSlimWithUnit : ASSET_MANAGEMENT_CONTEXT + "asset-name-unit-slim",
                getAssetsWithGRItemId : ASSET_MANAGEMENT_CONTEXT + "asset-gritem",
                getTransferableAssetsFromUnit : ASSET_MANAGEMENT_CONTEXT + "asset-transferable-unit",
                getTransferableAssetsFromUnitByProducts : ASSET_MANAGEMENT_CONTEXT + "asset-transferable-unit-by-products",
                getAssetsAssociatedWithGRItem : ASSET_MANAGEMENT_CONTEXT + "asset-gritem",
                getAssetRecoveryList : ASSET_MANAGEMENT_CONTEXT + "asset-recovery-unit-status",
                recoverAssetList : ASSET_MANAGEMENT_CONTEXT + "recover-asset-list",
                getAssetRecoveryData : ASSET_MANAGEMENT_CONTEXT + "asset-recovery-data",
                submitRecovery : ASSET_MANAGEMENT_CONTEXT + "submit-asset-recovery",
                assetStatusTypeList : ASSET_MANAGEMENT_CONTEXT + "asset-status",
                getAllAssetForUnit : ASSET_MANAGEMENT_CONTEXT + "asset-unit",
                alterAssetStatus : ASSET_MANAGEMENT_CONTEXT + "alter-asset-status",
                approveAssetRecoveryList : ASSET_MANAGEMENT_CONTEXT + "approve-recover-asset-list",
                assetPrintTag : ASSET_MANAGEMENT_CONTEXT + "asset-print-tagValue",
                createBackDatedAsset : ASSET_MANAGEMENT_CONTEXT + "create-backdated-asset",
                getMonkAssets : ASSET_MANAGEMENT_CONTEXT + "get-monk-ingredients-assets",
                autoToGrMonkAssets : ASSET_MANAGEMENT_CONTEXT + "auto-transfer-monk-ingredients",
                getMonkIngredientsStock : ASSET_MANAGEMENT_CONTEXT + "monk-ingredients-stock",
                bulkPrintAssetTag : ASSET_MANAGEMENT_CONTEXT + "bulk-asset-print-tagValue",
                updateAssetName: ASSET_MANAGEMENT_CONTEXT + "edit-asset-name",
                markNotFoundInAudit : ASSET_MANAGEMENT_CONTEXT + "not-found-in-audit",
                getNonAssetStockInAssetInventory : ASSET_MANAGEMENT_CONTEXT + "get-non-assets-in-asset-inventory",
                convertNonAssetStockcInAssetInentory : ASSET_MANAGEMENT_CONTEXT + "convert-non-assets-in-asset-inventory",
                saveNonScannableProducts : ASSET_MANAGEMENT_CONTEXT + "save-non-scannable-products",
                getNonScannableProducts : ASSET_MANAGEMENT_CONTEXT + "get-non-scannable-products" ,
                stockEventUnit: ASSET_MANAGEMENT_CONTEXT+"stock-event-unit" ,
                faSubmitTransferOut:ASSET_MANAGEMENT_CONTEXT+"fa-submit-transfer-out" ,
                stockEvent:ASSET_MANAGEMENT_CONTEXT+"stock-event",
                resetAssetCache:ASSET_MANAGEMENT_CONTEXT+"reset-asset-byId"
            },
            unitManagement: {
                unitDetail: UNIT_MANAGEMENT_ROOT_CONTEXT + "unit-detail",
                unitDetails: UNIT_MANAGEMENT_ROOT_CONTEXT + "unit-details",
                userUnits : MASTER_UNIT_MANAGEMENT + "user/units",
            },
            unitMetadata: {
                unitData: UNIT_METADATA_ROOT_CONTEXT + "unit-data",
                unitProductData: UNIT_METADATA_ROOT_CONTEXT + "unit-product-data",
                metadataCategories: UNIT_METADATA_ROOT_CONTEXT + "metadata-categories",
                allUnitsList: UNIT_METADATA_ROOT_CONTEXT + "all-units-list",
                getAllTaxCategories: UNIT_METADATA_ROOT_CONTEXT + "tax-categories",
                getAllCategoriesTax: UNIT_METADATA_ROOT_CONTEXT + "categories-tax-data",
                listTypes: UNIT_METADATA_ROOT_CONTEXT + "listTypes",
                regions : UNIT_METADATA_ROOT_CONTEXT + "regions",
                unitClosureState : UNIT_METADATA_ROOT_CONTEXT + "closure/get-by-unitId",
                checkStateSCM: ATTRIBUTE_MANAGEMENT_ROOT_CONTEXT+"/validate-state",
                sendUnitClosureTaskNotification : UNIT_METADATA_ROOT_CONTEXT + "send-unit-closure-task-done-notification"
            },
            taxMetadata: {
            	getTaxCodes: TAX_METADATA_ROOT_CONTEXT + "tax"
            },
            productManagement: {
                productDetail: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product",
                productDetailV2: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "user-creation-product",
                cancelProductCreation: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "cancel-product",
                productDetails: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "products",
                getUserProducts: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "get-user-created-products",
                activateProduct: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product-activate",
                deactivateProduct: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product-deactivate",
                archiveProduct: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product-archive",
                uploadProductImage: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "upload-product-image",
                packaging: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "packaging",
                packagings: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "packagings",
                packagingMap: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "packaging-map",
                packagingMapping: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product-packaging-mapping",
                packagingMappings: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product-packaging-mappings",
                defaultPackagingMappings: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "default-product-packaging-mappings",
                packagingMappingsByProduct: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product-packaging-mapping-by-product",
                deactivateMapping: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product-packaging-mapping-deactivate",
                activateMapping: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product-packaging-mapping-activate",
                skuDetails: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "skus",
                skuDetailsByProduct: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "skus-by-products",
                skuDetailsByProductId: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "skus-by-product-id",
                skuDetailsByUnitAndProduct: PRODUCT_MANAGEMENT_ROOT_CONTEXT +"skus-by-unit-product",
                skuDetail: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku",
                getAllSkuRequests: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku/get-all-requests",
                getAllSkuStatus: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku/get-all-status",
                addSkuDetail: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku/add",
                addSkuDetailV2: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku/add/V2",
                addSkuDetailRequest: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku/add-user",
                updateSkuDetail: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku/update",
                updateSkuDetailV2: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku/update/V2",
                updateSkuDetailRequest: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku/update-user",
                cancelSkuRequest: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku/cancel-user",
                activateSKU: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-activate",
                deactivateSKU: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-deactivate",
                uploadSkuImage: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "upload-sku-image",
                vendorDetails: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "vendor-details",
                unitVendorDetails: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "vendor-details/unit",
                getUnitProductVendors: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "vendor-details/unit/products",
                skuPackagingMapping: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-packaging-mapping",
                skuPackagingMappings: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-packaging-mappings",
                skuPriceUpdateEventGet: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-price-update-event/get",
                skuPriceDataDownload: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-price-data/download",
                skuPriceUpdateEventUpload: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-price-update-event/upload",
                skuPriceUpdateEventCancel: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-price-update-event/cancel",
                skuPriceUpdateEventReject: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-price-update-event/reject",
                skuPriceUpdateEventApprove: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-price-update-event/approve",
                skuPriceUpdateEventUpdate: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-price-update-event/update",
                skuPriceUpdateEventGetEntries: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-price-update-event/get/entries",
                deactivateSkuPackaging: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-packaging-mapping-deactivate",
                activateSkuPackaging: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sku-packaging-mapping-activate",
                defaultSkuPackaging: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "set-default-sku-packaging-mapping",
                defaultProductPackaging: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "set-default-product-packaging-mapping",
                planDetailForProduct: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "productDetail/productId",
                getUnitSkuPackagingMappings: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "unit-sku-packaging-mappings",
                updateSubCategoryShelfLife : PRODUCT_MANAGEMENT_ROOT_CONTEXT + "sub-category-shelf-life",
                getProducts: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "get-product-basic-detail",
                getRegionProductPackagings: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "get-region-product-packagings",
                getProductPackagingMapping: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "get-product-packaging-mapping",
                mapRegionProductPackaging: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "map-region-product-packaging",
                getAllDerivedProducts: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "get-all-derived-products",
                getDerivedProductsMappingForUnit: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "get-derived-products-mapping-for-unit",
                getAllUnitsForProductMapping: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "get-all-units-for-product-mapping",
                updateDerivedMappingData: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "update-derived-mapping-data",
                getDerivedProductCloningForUnits: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "get-derived-product-cloning-for-units",
                getUnitsCloningForProduct: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "get-units-cloning-for-product",
                cloneDerivedProductMappings: PRODUCT_MANAGEMENT_ROOT_CONTEXT + "clone-derived-product-mappings",
            },
            stockManagement: {
                postStockingList: STOCK_MANAGEMENT + "post-stocking-list",
                checkOpening: STOCK_MANAGEMENT + "check-opening",
                getStockingList: STOCK_MANAGEMENT + "get-stocking-list",
                kettleDayCloseFromSumo: STOCK_MANAGEMENT + "refresh-kettle-dayClose",
                wastageEvents: STOCK_MANAGEMENT + "wastage-events",
                wastageEventsForADay: STOCK_MANAGEMENT + "wastage-events-for-a-day",
                wastageEventsForDays: STOCK_MANAGEMENT + "get-wastage-events-for-Days",
                wastageEvent: STOCK_MANAGEMENT + "wastage-event",
                cancelWastageEvent: STOCK_MANAGEMENT + "cancel-wastage-event",
                closingInitiated: STOCK_MANAGEMENT + "closing-initiated",
                getCurrentStock: STOCK_MANAGEMENT + "get-current-stock",
                getLiveStock: STOCK_MANAGEMENT + "get-live-stock",
                getInTransitStock:STOCK_MANAGEMENT +"get-current-in-transit-stock",
                getDayWiseExpiryProduct:STOCK_MANAGEMENT +"get-day-wise-expiry-product",
                inventoryUpdated: STOCK_MANAGEMENT + "check-inventory-loaded",
                predictExpectedValues: STOCK_MANAGEMENT + "get-expected-values",
                updateInventory: STOCK_MANAGEMENT + "update-inventory",
                checkPendingGR: STOCK_MANAGEMENT + "pending-gr-check",
                checkWastageEntered: STOCK_MANAGEMENT + "wastage-entered-check",
                checkPendingTransfers: STOCK_MANAGEMENT + "check-pending-transfers",
                validateStockEvent: STOCK_MANAGEMENT + "validate-stock-event",
                validateStockEvents: STOCK_MANAGEMENT + "validate-stock-events",
                checkFixedAssetDayClose: STOCK_MANAGEMENT + "fa-day-close",
                checkPendingApprovals: STOCK_MANAGEMENT + "fetch-approvals",
                checkFoundPendingApprovals: STOCK_MANAGEMENT + "get-found-asset-approval",
                processRequest: STOCK_MANAGEMENT + "process-approval-request",
                plannedStockEvent: STOCK_MANAGEMENT + "planned-stock-event",
                updateStockEventCalendar: STOCK_MANAGEMENT + "update-stock-event-calendar",
                addStockEvent : STOCK_MANAGEMENT + "add-planned-stock-event",
                removeDuplicateKeys : STOCK_MANAGEMENT + "remove-duplicate-keys",
                cafeProductStockAtHand: STOCK_MANAGEMENT + "get-scm-product-stock",
                generateWastageSheet : STOCK_MANAGEMENT + "generate-wastage-sheet",
                getPendingSpecialRos: STOCK_MANAGEMENT + "pending-milk-bread-ros",
                getCafeVarianceEditProducts: STOCK_MANAGEMENT + "get-cafe-variance-edit-products",
                submitCafeVarianceEdit: STOCK_MANAGEMENT + "submit-cafe-variance-edit",
                getUnitsForVarianceAcknowledge: STOCK_MANAGEMENT + "units-variance-acknowledge",
                getVarianceAcknowledgeList : STOCK_MANAGEMENT + "acknowledge-variance-list",
                acknowledgeVariance : STOCK_MANAGEMENT + "acknowledge-variance",
                getVarianceDetails : STOCK_MANAGEMENT + "get-variance-details",
                checkVarianceAcknowledgement : STOCK_MANAGEMENT + "check-variance-acknowledgement",
                cronTesting : STOCK_MANAGEMENT + "variance-cron-testing",
                getUnitsForStockTakeThroughApp : STOCK_MANAGEMENT + "get-units-for-stock-take",
                checkForStockTakeSumoDayClose : STOCK_MANAGEMENT + "checkForStockTakeSumoDayClose"
            },
            categoryManagement: {
                category: CATEGORY_MANAGEMENT_ROOT_CONTEXT + "category",
                categories: CATEGORY_MANAGEMENT_ROOT_CONTEXT + "categories",
                attributeMapping: CATEGORY_MANAGEMENT_ROOT_CONTEXT + "category-attribute-mapping",
                attributeMappings: CATEGORY_MANAGEMENT_ROOT_CONTEXT + "category-attribute-mappings",
                attributeValueMappings: CATEGORY_MANAGEMENT_ROOT_CONTEXT + "category-attribute-values",
                attributeValueMapping: CATEGORY_MANAGEMENT_ROOT_CONTEXT + "category-attribute-value",
                attributeValuesByCategory: CATEGORY_MANAGEMENT_ROOT_CONTEXT + "category-attribute-values-by-category",
                activateCategoryAttributeValue: CATEGORY_MANAGEMENT_ROOT_CONTEXT + "category-attribute-value-activate",
                deactivateCategoryAttributeValue: CATEGORY_MANAGEMENT_ROOT_CONTEXT + "category-attribute-value-deactivate"
            },
            referenceOrderManagement: {
                referenceOrderFind: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "reference-order-find",
                referenceOrder: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "reference-order",
                newReferenceOrder: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "new-reference-order",
                referenceOrderEstimates: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "reference-order-estimates",
                suggestingOrderEstimates: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "suggesting-order-estimates",
                suggestingOrderEstimatesV1: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "suggesting-order-estimates-v1",
                salesPercentage: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "sales-percentage",
                salesPercentageForUnits: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "sales-percentage-for-units",
                specialProductEstimates: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "special-products-estimates",
                expiryProductData: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"expiry-product",
                getNewExpiryProductData: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"get-new-expiry-products",
                getUnitOrderingSchedule: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"get-unit-ordering-schedule",
                addUnitOrderingSchedule: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"add-unit-ordering-schedule",
                getFountain9Units: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"get-fountain9-units",
                checkForF9Orders: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"check-for-f9-orders",
                generateF9SalesReport: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"generate-fountain9-comparison-report",
                getAllOrderingSchedules: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"get-all-ordering-schedules",
                getFulfilmentUnits: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"get-fulfilment-units",
                availableUnitsForSchedule: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"available-units-for-schedule",
                cloneUnitOrderingSchedule: REFERENCE_MANAGEMENT_ROOT_CONTEXT+"clone-unit-ordering-schedule",
                getScmProductsConsumptionAverage: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "get-scm-products-consumption-average",
                getNonEditableProductsInSuggestiveOrdering: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "get-freeze-suggested-quantity",
                getMenuProductsConsumptionAverage: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "get-menu-products-consumption-average",
                checkSuggestiveOrderingStrategyAvailable: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "check-suggestive-ordering-strategy-available",
                getScmProductsFromMenuDateWise: KETTLE_POS_METADATA_ROOT_CONTEXT + "get-menu-scm-products"
            },
            serviceOrderManagement:{
            	costCentersAll: SERVICE_MANAGEMENT_ROOT_CONTEXT + "cost-center/all",
                costCenters: SERVICE_MANAGEMENT_ROOT_CONTEXT + "cost-center",
                businessCostCenter: SERVICE_MANAGEMENT_ROOT_CONTEXT + "business-cost-center",
                costCentersCreate: SERVICE_MANAGEMENT_ROOT_CONTEXT + "cost-center-create",
                costElementCreate: SERVICE_MANAGEMENT_ROOT_CONTEXT + "cost-element-create",
                createEmployeeCostCenterMap: SERVICE_MANAGEMENT_ROOT_CONTEXT + "create-employee-cost-center-map",
                createBusinessCostCenterMap: SERVICE_MANAGEMENT_ROOT_CONTEXT + "business-cost-center-mapping/create",
                deactivateEmployeeBusinessCostCenterMap: SERVICE_MANAGEMENT_ROOT_CONTEXT + "employee-business-cost-center/deactivate",
                createServiceOrder: SERVICE_MANAGEMENT_ROOT_CONTEXT + "create-service-order",
                serviceVendors: SERVICE_MANAGEMENT_ROOT_CONTEXT + "service-vendors",
                createdOrders: SERVICE_MANAGEMENT_ROOT_CONTEXT + "created-orders",
                createdOrdersShort: SERVICE_MANAGEMENT_ROOT_CONTEXT + "created-orders-short",
                pendingOrders: SERVICE_MANAGEMENT_ROOT_CONTEXT + "pending-orders",
                approveSO: SERVICE_MANAGEMENT_ROOT_CONTEXT + "approve-so",
                rejectSO: SERVICE_MANAGEMENT_ROOT_CONTEXT + "reject-so",
                cancelSO: SERVICE_MANAGEMENT_ROOT_CONTEXT + "cancel-so",
                closeSO: SERVICE_MANAGEMENT_ROOT_CONTEXT + "close-so",
                getSRIdsForSO: SERVICE_MANAGEMENT_ROOT_CONTEXT + "get-sr-ids-for-so",
                getCostElementsAll: SERVICE_MANAGEMENT_ROOT_CONTEXT+"cost-element/all",
                costElementStatus:  SERVICE_MANAGEMENT_ROOT_CONTEXT + "costElement-status",
                getListData: SERVICE_MANAGEMENT_ROOT_CONTEXT + "getCostElementList",
                createCostElement: SERVICE_MANAGEMENT_ROOT_CONTEXT + "create-cost-element",
                addListDetail: SERVICE_MANAGEMENT_ROOT_CONTEXT + "add-list-detail",
                updateListDetail : SERVICE_MANAGEMENT_ROOT_CONTEXT + "update-list-detail",
                addSubCategoryData : SERVICE_MANAGEMENT_ROOT_CONTEXT + "add-subCategory-data",
                updateSubCategoryData : SERVICE_MANAGEMENT_ROOT_CONTEXT + "update-subCategory-data",
                addSubSubCategoryData : SERVICE_MANAGEMENT_ROOT_CONTEXT + "add-subSubCategory-data",
                updateSubSubCategoryData : SERVICE_MANAGEMENT_ROOT_CONTEXT + "update-subSubCategory-data",
                getMappedVendorList: SERVICE_MANAGEMENT_ROOT_CONTEXT + "mapped-costcenter",
                getMappedCostElements:SERVICE_MANAGEMENT_ROOT_CONTEXT + "mapped-costelements",
                getSelectedCostElement: SERVICE_MANAGEMENT_ROOT_CONTEXT + "selected-costelement",
                getTagList: SERVICE_MANAGEMENT_ROOT_CONTEXT + "tag-list",
                getTagNamesList: SERVICE_MANAGEMENT_ROOT_CONTEXT + "tag-names-list",
                getVendorData: SERVICE_MANAGEMENT_ROOT_CONTEXT + "get-vendorData",
                changeStatus: SERVICE_MANAGEMENT_ROOT_CONTEXT + "change-status",
                checkUnitBudget: SERVICE_MANAGEMENT_ROOT_CONTEXT + "check-unit-budget",
                getDepartmentData: SERVICE_MANAGEMENT_ROOT_CONTEXT + "get-department-data-unit",
                budgetCategoryList: SERVICE_MANAGEMENT_ROOT_CONTEXT + "budget-category-list",
                uploadSoDocument: SERVICE_MANAGEMENT_ROOT_CONTEXT + "upload-so-document",
                downloadSampleBilkSO: SERVICE_MANAGEMENT_ROOT_CONTEXT + "download-bulk-so",
                readUploadedSo: SERVICE_MANAGEMENT_ROOT_CONTEXT + "upload-bulk-so",
                uploadRequiredDocuments: SERVICE_MANAGEMENT_ROOT_CONTEXT + "upload-required-documents",
                showRequiredDocuments: SERVICE_MANAGEMENT_ROOT_CONTEXT + "show-required-documents",
                uploadApprovalOfHod: SERVICE_MANAGEMENT_ROOT_CONTEXT + "upload-approval-of-hod",
                removeUserToCostCenterMapping : SERVICE_MANAGEMENT_ROOT_CONTEXT + "remove-employee-cost-center-mapping",
                getCostElementVendorList: SERVICE_MANAGEMENT_ROOT_CONTEXT + "get-vendor-for-cost-element",
                getBccCCMapping : SERVICE_MANAGEMENT_ROOT_CONTEXT + "get-bcc-cc-mapping"
            },

            serviceMappingManagement:{
            	getCostElementData: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "cost-element-data",
            	getVendorList: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-vendor-list",
            	addCostElementToVendorMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-costelement-to-vendor",
            	getCostElementToVendorMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "costelement-to-vendor",
            	updateCostelementVendorMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-costelement-to-vendor",
            	getVendorToCostElementMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "vendor-to-costElement",
            	addVendorToCostElementMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-vendor-to-costelement",
            	getCostCentreList: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-costcentre-list",
            	addCostElementToCostCentreMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-costelement-to-costcenter",
            	getCostElementToCostCenterMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "costelement-to-costcenter",
            	updateCostelementCostCenterMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-costelement-to-costcenter",
            	addCostCentreToCostElementMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-costcentre-to-costelement",
            	getCostCenterToCostElementMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "costcenter-to-costelement",
            	getPricedCostElements: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-priced-costelements",
            	updateStatusCostelementPriceMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-status-costelement-price",
            	addCostelementPriceMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-costelement-mapping-price",
            	updatePriceCostElementMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT +"update-price-costelement-mapping",
                clonePricingFromVendor: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "vendor-price-clone",

                addMasterDocuments: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-master-document",
                addAdditionalDocuments: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-additional-docs",
                addCostElementToDocumentMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-costelement-to-document",
                getCostElementToDocumentMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "costelement-to-document",
                getDocumentList: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-document-list",
                updateCostElementDocumentMapping: SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-costelement-to-document"
            },

        	capexManagement: {
        		getAllCapexData: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "getAllCapexData",
        		getVersionList: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "getVersionList",
        		validateUnit: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "validateUnit",
        		createCapex: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "createCapex",
        		getCapexFile: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "getCapexFile",
        		validateCapex: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "validate-capex",
                    uploadCapexFile: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "uploadCapexFile",
        		changeCapexStatus: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "change-capex-status",
        		approveCapexBudget: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "approve-capex-budget",
        		editCapexRequest: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "edit-capex-request",
        		getDepartmentList: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-department-budget",
        		getDeptBudgetForApproval: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "show-capex-budget",
        		approvalStatusChange: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "approval-status-change",
        		fetchBudgetDetails: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "fetch-budget-details",
        		uploadClosureSheet: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "upload-closure-sheet",
        		getCapexClosureFile: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "download-closure-sheet",
        		getClosureComment: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-Closure-Comment",
        		initiateClosureState: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "initiate-closure-state",
        		getBudgetComaparision: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-budget-comparision",
                getSOByDepartment: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-so-by-department",
                getSoByCapex  : CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-so-by-capex",
                getPoByCapex  : CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-po-by-capex",
                getCapexValidationBySoPo : CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-capex-validation-by-so-po",
                getSoPoLevelSummary : CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-so-po-level-summary-by-capex",
                getSoLevelSummaryByCapex  :CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-so-level-summary-by-capex",
                getPOByDepartment: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-po-item-level-data",
                getPOLevelSummaryByCapex: CAPEX_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-po-level-summary-by-capex",

            },
            productProjectionsManagement: {
                generateProductProjections: PRODUCT_PROJECTIONS_ROOT_CONTEXT + "generate-product-projections",
                readUploadedFile: PRODUCT_PROJECTIONS_ROOT_CONTEXT + "read-uploaded-file",
                generateProjectionsForUnits: PRODUCT_PROJECTIONS_ROOT_CONTEXT + "generate-projections-for-units"
            },
            requestOrderManagement: {
                requestOrderFind: REQUEST_MANAGEMENT_ROOT_CONTEXT + "request-order-find",
                requestOrder: REQUEST_MANAGEMENT_ROOT_CONTEXT + "request-order",
                multipleRequestOrder: REQUEST_MANAGEMENT_ROOT_CONTEXT + "multiple-request-order",
                pendingRequestOrders: REQUEST_MANAGEMENT_ROOT_CONTEXT + "request-orders-pending",
                pendingRequestOrderShort: REQUEST_MANAGEMENT_ROOT_CONTEXT + "request-orders-pending-short",

                pendingOrdersByFulfillmentDate: REQUEST_MANAGEMENT_ROOT_CONTEXT + "request-orders-by-fulfillment-date",
                pendingOrdersByFulfillmentDateShort: REQUEST_MANAGEMENT_ROOT_CONTEXT + "request-orders-by-fulfillment-date-short",
                acknowledgeOrders: REQUEST_MANAGEMENT_ROOT_CONTEXT + "acknowledge-orders",
                downloadOrder: REQUEST_MANAGEMENT_ROOT_CONTEXT + "download-order",
                cancelRequestOrder: REQUEST_MANAGEMENT_ROOT_CONTEXT + "request-order-cancel",
                getSpecializedOrderReport: REQUEST_MANAGEMENT_ROOT_CONTEXT + "special-request-orders-for-day",
                createPurchaseOrder: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "create-po",
                getPurchaseOrders: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "get-purchase-orders",
                getPendingPOs: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "get-pending-orders",
                getCreatedOrders: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "get-created-orders",
                approvePO: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "approve-po",
                extendPO: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "extend-po",
                rejectPO: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "reject-po",
                cancelPO: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "cancel-po",
                closePO: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "close-po",
                purchaseConsumption:PURCHASE_MANAGEMENT_ROOT_CONTEXT + "consumption",
                startPlanning: REQUEST_MANAGEMENT_ROOT_CONTEXT + "start-planning",
                updatePlanning: REQUEST_MANAGEMENT_ROOT_CONTEXT + "update-planning",
                getPlanItemsForSemiFinishedProduct: REQUEST_MANAGEMENT_ROOT_CONTEXT + "semi-finished/get-items",
                submitPrepPlan: REQUEST_MANAGEMENT_ROOT_CONTEXT + "prep-plan-submit",
                getPrepPlans: REQUEST_MANAGEMENT_ROOT_CONTEXT + "prep-plans-get",
                acknowledgePlanorders: REQUEST_MANAGEMENT_ROOT_CONTEXT + "acknowledge-plan-orders",
                downloadPlanOrder: REQUEST_MANAGEMENT_ROOT_CONTEXT + "download-plan-order",
                downloadPlanOrders: REQUEST_MANAGEMENT_ROOT_CONTEXT + "download-plan-orders",
                downloadPlanOrdersZip: REQUEST_MANAGEMENT_ROOT_CONTEXT + "download-plan-orders-zip",
                downloadPlanOrdersBulk : REQUEST_MANAGEMENT_ROOT_CONTEXT + "download-plan-order-all",
                plansByFulfillmentDate: REQUEST_MANAGEMENT_ROOT_CONTEXT + "plans-by-fulfillment-date",
                extraGrEligibility: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "extra-gr-eligibility",
                updateTag: REQUEST_MANAGEMENT_ROOT_CONTEXT + "update-tag",
                sendVendorRONotification: REQUEST_MANAGEMENT_ROOT_CONTEXT + "cron-to-send-consolidated-ro-email-to-vendor",
                uploadExcessPlanning:REQUEST_MANAGEMENT_ROOT_CONTEXT+"upload-excess-planning",
                getRoItemsByPlanId:REQUEST_MANAGEMENT_ROOT_CONTEXT+"get-ro-items-by-planId",
                updateAdjustedQuantities:REQUEST_MANAGEMENT_ROOT_CONTEXT+"update-adjusted-quantities",
                getSummaryForProductionPlanning:REQUEST_MANAGEMENT_ROOT_CONTEXT+"get-production-summary",
                getDepartmentBudgetData:PURCHASE_MANAGEMENT_ROOT_CONTEXT+"get-department-budget-data",
                getDepartmentBudgetDataV2:PURCHASE_MANAGEMENT_ROOT_CONTEXT+"get-department-budget-data/V2",
                getRequestOrdersByIds : REQUEST_MANAGEMENT_ROOT_CONTEXT + "request-orders-find",
                getCurrentPriceForFullfilmentUnit : REQUEST_MANAGEMENT_ROOT_CONTEXT + "get-product-prices-fulfillment-unit",
                getLastWeekAverageQty : REQUEST_MANAGEMENT_ROOT_CONTEXT + "get-last-week-average",
                getProductDiscontinuedStatus: REQUEST_MANAGEMENT_ROOT_CONTEXT + "get-product-dis-continued-status" ,
                updatePoBeforeApproval : PURCHASE_MANAGEMENT_ROOT_CONTEXT + "update-approval-po",
                previewPlanOrders: REQUEST_MANAGEMENT_ROOT_CONTEXT + "preview-plan-orders"
            },
            transferOrderManagement: {
                transferOrder: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "transfer-order",
                transferOrderAndGr: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "transfer-order-gr",
                clubbedTransferOrder: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "clubbed-transfer-order",
                transferOrderFind: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "transfer-order-find",
                states: SCM_METADATA_ROOT_CONTEXT + "states",
                pendingTransferOrderFind: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "pending-transfer-order-find",
                cancelTransferOrder: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "transfer-order-cancel",
                getTorqusTO: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "get-torqus-transfer",
                externalTransfer: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "external-transfer-order",
                approveExternalTransfer: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "approve-external-transfer",
                transferOrderExpiryCheck: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "transfer-order-expiry-check",
                getEwayBillNumber: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "eway-bill-number",
                incrementalTransferOrder: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "incremental-transfer-order",
                bulkClubbedTransferOrder: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "bulk-transfer-order",
                getSkuToProducts: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "get-sku-to-products",
                findBulkEvent: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "find-bulk-event",
                findBulkEventsShort: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "find-bulk-event-short",
                getTransferOrdersView: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "get-transfer-orders-view",
                setTransferInvoices: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "set-transfer-invoices",
                getAssetTransfers: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "get-asset-transfers",
                updateAssetStatus: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "update-asset-status",
                bulkupdateAssetStatus: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "bulk-update-asset-status",
                downloadBulkStandaloneTemplate: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "download-bulk-standalone-Template",
                getBulkStandAloneProducts: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "get-products-for-standalone",
                parseDistributionSheet: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "parse-bulk-standalone-sheet",
                bulkStandAloneTransfer: TRANSFER_MANAGEMENT_ROOT_CONTEXT + "bulk-standalone-transfer" ,
                downloadEInvoiceJson : TRANSFER_MANAGEMENT_ROOT_CONTEXT + "generate-e-invoice-json",
                findMissingDistanceMappings : TRANSFER_MANAGEMENT_ROOT_CONTEXT + "find-missing-distance-mappings",
                uploadEInvoiceExcell : TRANSFER_MANAGEMENT_ROOT_CONTEXT + "upload-e-invoice-excell",
                getEInvoiceData : TRANSFER_MANAGEMENT_ROOT_CONTEXT + "get-e-invoice-data",
                initiateFaTransfer : TRANSFER_MANAGEMENT_ROOT_CONTEXT+"initiate-fa-transfer" ,
                changeFaTranferProduct: TRANSFER_MANAGEMENT_ROOT_CONTEXT+"change-fa-tranfer-product"
            },
            goodsReceivedManagement: {
                pendingGrs: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "good-received-pending",
                pendingDisputedGrs: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "good-received-pending-disputed",
                raisedDisputedGrs: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "good-received-raised-disputed",
                goodReceived: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "good-received",
                settleGR: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "good-received-settle",
                cancelGR: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "good-received-cancel",
                rejectedFoundGR: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "rejected-good-received-found",
                rejectedLostGR: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "rejected-good-received-lost",
                declineGR: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "rejected-good-received-decline",
                acceptDeclinedGR: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "accept-good-received-declined",
                goodReceivedFind: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "good-received-find",
                isGrSettled: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "is-gr-settled",
                findUpdationReasons:GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-grItem-quantity-update-reasons",
                getGrsView : GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "get-grs-view",
                getMilkGrsForPayment : GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT+ "find-milk-grs-for-payment",
                getMilkInvoicesForPayment : GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT+ "find-milk-invoices-for-payment",
                uploadPOD:GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "upload-POD",
                uploadProofOfRejection : GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "upload-proof-of-rejection",
                getPorImageUrls: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "por-image-urls",
                autoAcknowledgeAndTransferSpecializedROs : REQUEST_MANAGEMENT_ROOT_CONTEXT +"auto-acknowledge-auto-transfer-specialized-ros",
                getUrl : GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "get-url",
                uploadVendorInvoice : GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "upload-vendor-invoice",
            //Here on only Vendor GR related mappings
                createVendorGR: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "create-vendor-gr",
                cancelVendorGR: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "cancel-vendor-gr",
                uploadGR: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "upload-gr",
                findVendorReceivings: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-vendor-grs",
                findVendorReceivingsRejected: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-vendor-grs-rejected",
                findVendorGrsForPo: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-vendor-gr-for-po",
                findRegularVendorGrs: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-regular-vendors-grs",
                approveRegularVendorGrs: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "approve-regular-vendor-gr",
                findAssetsForPayment: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-asset-for-pr",

                approveVendorGR: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "approve-vendor-gr",
                findVendorGRsForPayment: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-vendor-grs-for-payment",
                setVendorGRsForNoPayment: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "set-vendor-grs-for-no-payment",
                getGrsForReverseTo: GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "reverse-to-grs",
                initiateGREvent : GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "initiate-gr-event",
                getGrEvent : GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "get-gr-event",
                cancelGrEvent : GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "cancel-gr-event",
                settleGrEvent  : GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "settle-gr-event"
            },
            serviceReceivedManagement:{
                bcc: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "bcc",
                createReceiving: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "create-service-receive",
                createReceivings: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "create-service-receives",
                findReceivings: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-service-receive",
                findReceivingsShort: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-service-receive-short",
                getLinkedSrForSo: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "get-Sr-For-So",
                cancel: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "cancel-service-receive",
                findReceivingsForPayment: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-service-receive-for-payment",
                findReceivingsByPR: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "find-service-receive-for-payment-request",
                createPaymentRequest: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "create-payment-request",
                approveSR : SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "approve-sr",
                sendMbEmail : SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "send-mb-email",
                getMinMaxDateForSrs: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "get-min-max-date-for-srs",
                reCheckAllSrsForAdvance: SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "recheck-all-srs-for-advance",
                generateSrSheet : SERVICE_RECEIVED_MANAGEMENT_ROOT_CONTEXT + "generate-sr-sheet"
            },
            vehicleData: {
            	vehicleList: VEHICLE_MANAGEMENT + "vehicle-list",
            	saveVehicle: VEHICLE_MANAGEMENT + "addVehicle",
            	singleVehicle:  VEHICLE_MANAGEMENT + "get-Vehicle",
            	saveUpdatedVehicle: VEHICLE_MANAGEMENT + "saveUpdatedVehicle",
            },
            scmUnitManagement: {
                units: SCM_UNIT_MANAGEMENT_ROOT_CONTEXT + "units",
                businessDate: SCM_UNIT_MANAGEMENT_ROOT_CONTEXT + "business-date"
            },
            users: {
                activeUsers: USER_SERVICES_ROOT_CONTEXT + "all",
                login: USER_SERVICES_ROOT_CONTEXT + "login",
                switchUnit: USER_SERVICES_ROOT_CONTEXT + "unit-session-login",
                changePassCode: USER_SERVICES_ROOT_CONTEXT + "changePasscode",
                logout: USER_SERVICES_ROOT_CONTEXT + "logout",
                purchaseRoles: USER_SERVICES_ROOT_CONTEXT + "purchaseRoles",
                getEmpByAcl: USER_SERVICES_ROOT_CONTEXT + "get/emp-by-acl",
            },
            vendorManagement: {
                vendors: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendors",
                vendorsTrimmed: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendors-trimmed",
                vendor: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor",
                allVendorName: VENDOR_MANAGEMENT_ROOT_CONTEXT + "all-Vendor",
                allVendorNameNew: VENDOR_MANAGEMENT_ROOT_CONTEXT + "all-Vendor-new",
                vendorType: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor-type",
                vendorRegistration: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/registrations",
                vendorByStatus: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/status",
                vendorByShort: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/status/short",
                vendorDetails: VENDOR_MANAGEMENT_ROOT_CONTEXT + "findVendorDetail",
                vendorRegistrationByStatus: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/registrations/status",
                submitVendorDocument: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/submit-document",
                vendorRequest: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/registration/request",
                vendorRequestCancel: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/registration/request/cancel",
                vendorActivate: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor-activate",
                vendorEdit: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor-edit",
                vendorDeactivate: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor-deactivate",
                getPendingOrdersToBlockVendor: VENDOR_MANAGEMENT_ROOT_CONTEXT + "get-pending-orders-to-block-vendor",
                approveVendor: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor-request-approve",
                unitVendors: VENDOR_MANAGEMENT_ROOT_CONTEXT + "unit-vendors",
                unitVendorAdd: VENDOR_MANAGEMENT_ROOT_CONTEXT + "unit-vendor-add",
                unitVendorUpdate: VENDOR_MANAGEMENT_ROOT_CONTEXT + "unit-vendor-update",
                unitVendorActivate: VENDOR_MANAGEMENT_ROOT_CONTEXT + "unit-vendor-activate",
                unitVendorDeactivate: VENDOR_MANAGEMENT_ROOT_CONTEXT + "unit-vendor-deactivate",
                getVendor: VENDOR_MANAGEMENT_ROOT_CONTEXT + "get-vendor",
                getVendorStatus: VENDOR_MANAGEMENT_ROOT_CONTEXT + "get-vendor-status",
                downloadDocument: VENDOR_MANAGEMENT_ROOT_CONTEXT + "download-document",
                getDocumentDetailById: VENDOR_MANAGEMENT_ROOT_CONTEXT + "get-document-detail-by-id",
                blockPayments: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/payments/block",
                unBlockPayments: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/payments/un-block",
                downloadDebitBalanceSheet: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/debit-balance-sheet/download",
                uploadDebitBalanceSheet: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/debit-balance-sheet/upload",
                uploadTDSMailFile: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/tds-mailing-sheet/upload",
                saveDebitBalances: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/save-debit-balances",
                getAccount: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor/get-account",
                serviceVendors: VENDOR_MANAGEMENT_ROOT_CONTEXT + "service-vendors",
                dispatchLocations: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor-locations",
                saveCreditCycle: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor-credit-cycle",
                saveLeadTime: VENDOR_MANAGEMENT_ROOT_CONTEXT + "save-lead-time",
                partialDeactivateVendor: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor-deactivate-partial",
                reactivateVendor: VENDOR_MANAGEMENT_ROOT_CONTEXT + "vendor-reactivate",
                uploadTdsDocument: VENDOR_REGISTRATION_ROOT_CONTEXT + "upload-tds-document",
                getPriceHistory: VENDOR_MANAGEMENT_ROOT_CONTEXT + "get-price-history",
                updateUnblockTillDate: VENDOR_MANAGEMENT_ROOT_CONTEXT + "update-unblock-till-date",
                blockUnblockVendor: VENDOR_MANAGEMENT_ROOT_CONTEXT + "block-unblock-vendor",
                vendorDetailEditRequest : VENDOR_MANAGEMENT_ROOT_CONTEXT+ "vendor-detail-edit-request",
                validateVendorCompliances: VENDOR_MANAGEMENT_ROOT_CONTEXT+ "validate-vendor-compliances",
                checkVendorContractStatus : VENDOR_MANAGEMENT_ROOT_CONTEXT + "check-vendor-contract-status"
            },
            skuMapping: {
                getVendorsForUnit: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-vendors-for-unit",
                businessTypes: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-business-types",
                getVendorsForUnitTrimmed: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-vendors-for-unit-trimmed",
                getSkuForUnit: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "sku-for-unit",
                getVendorForBusiness: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "vendor-for-business",
                updateVendorForBusiness: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-vendor-for-business",
                getUnitForSku: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "unit-for-sku",
                updateUnitForSku: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-unit-for-sku",
                getProfileForUnit: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-skuProfile-for-unit",
                updateSkuProfileForUnit: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-sku-profile",
                updateSkuForUnit: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-sku-for-unit",
                getSkuForVendor: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "sku-for-vendor",
                getVendorForSku: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "vendor-for-sku",
                updateVendorForSku: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-vendor-for-sku",
                addVendorForSku: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-vendor-for-sku",
                addSkuForVendor: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-sku-for-vendor",
                addSkuForVendorAlias: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-sku-for-vendor-alias",
                getAllSku: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "all-sku",
                getAllUnit: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "all-unit",
                getAllVendors: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "all-vendors",
                getAllVendorsByBusinessType: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "all-vendors-by-businessType",
                searchSkuPrice: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "search-sku-price",
                searchVendorToSkuPrice: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "search-vendor-sku-price",
                updateSkuPrice: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-sku-price",
                addSkuPrice: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-sku-price",
                updateSkuPriceV2: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-sku-priceV2",
                addSkuPriceV2: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "add-sku-priceV2",
                updateSkuPriceStatus: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-sku-price-status",
                updateSkuLeadTime: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT +"update-sku-leadTime",
                cancelSkuPriceUpdate: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "cancel-sku-price-update",
                searchUnitSkuVendorMapping: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-unit-sku-vendor-mapping",
                updateUnitSkuVendorMapping: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-unit-sku-vendor-mapping",
                skuPriceAndTaxList: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "sku-price-and-tax-list",
                skuPriceAndTaxForProfile: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "sku-price-and-tax-list-for-profile",
                skusMappedToVendor: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "sku-mapped-for-vendor",
                skuPackaging: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "sku-packaging",
                vendorSites: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "vendor-sites",
                getUnitsByLocationId : SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "units-by-delivery-location",
                unitsByBusinessType: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "unit-by-business-type",
                getUnitDistance:SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "unit-distance-mapping",
                updateUnitDistanceMapping:SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT +"update-unit-distance-mapping",
                getSiblingSkusInventoryList : SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "sibling-skus-inventoy-list",
                getUnitSkuPackagingTaxMappings: SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "unit-sku-packaging-tax-mapping",
                updateUnitSkuPackagingTaxMappings : SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "update-unit-sku-packaging-tax-mapping",
                generateSkuPrice: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "generate/sku-price-request",
                getVendorRequestedPrice: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/get-request-price",
                processVendorRequestedPrice: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/process-price-request",
                previewVendorRequestedPrice: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/preview-request-price",
                previewVendorRequestedPriceV2: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/preview-request-price/v2",
                acceptVendorRequestedPrice: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/approve-request-price",
                acceptVendorRequestedPriceV2: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/approve-request-price/v2",
                getVendorContract: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/get-contract",
                getVendorContractV2: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/get-contract/V2",
                getWorkOrdersByContractId: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/get-work-orders",
                getItemsByWoId: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/get-items-by-woId",
                cancelVendorContract: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/cancel-contract",
                cancelVendorContractV2: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/cancel-contract/V2",
                contractTemplate: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/get-template",
                generateContractTemplate: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/generate-contract",
                generateContractTemplateV2: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/generate-contract/V2",
                mapContractWithDocument: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/map-contract-with-document",
                mapContractWithDocumentV2: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/map-contract-with-document/V2",
                getUrl: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/get-contract-document",
                triggerMail: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/trigger-mail",
                triggerMailV2: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/trigger-mail/V2",
                applyContract: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/apply-contract",
                applyContractV2: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/apply-contract/V2",
                getSkuWrtCategory : SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-sku-wrt-category",
                getSkuPricing : SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT + "get-sku-prices",
                initiateContract : VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "initiate/byPass-vendor-contract",
                saveByPassVendorContractItem : VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "save/byPass-vendor-contract-item",
                findSkuPriceMapping :VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "find/sku-price-mapping",
                getWorkOrders :VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "get/work-orders",
                savePriceApprovals : VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "submit/price-approvals",
                previewPricing : VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "preview-sku-pricing",
                deactivateSkuPrice : VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "submit/deactiavted-sku-prices",
                approveVendorContract : VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "approve/vendor-contract",
                getContractStatus : VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "get/bypass-status",
                uploadDocument : VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "upload-document",
                getPreviousPricesOfSkuByLocation : VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "get/previous-prices-of-sku",
                getPreviousPricesOfSkuByLocationForCustomer : VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "get/previous-prices-of-sku-for-customer",
                digitalSignature: VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT + "vendor/save-digital-signature"
            },
            userManagement: {
                usersForUnit: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "users/unit",
                activeUnitsForUser: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "user/units"
            },
            productionBooking: {
                calculateConsumption: PRODUCTION_BOOKING_ROOT_CONTEXT + "calculate-consumption",
                add: PRODUCTION_BOOKING_ROOT_CONTEXT + "add",
                bookings: PRODUCTION_BOOKING_ROOT_CONTEXT + "bookings",
                getLastBooking: PRODUCTION_BOOKING_ROOT_CONTEXT + "getLastBooking",
                generateRawMaterialExcel: PRODUCTION_BOOKING_ROOT_CONTEXT + "generate-raw-material-excel",
                cancel: PRODUCTION_BOOKING_ROOT_CONTEXT + "cancel"
            },
            pricedetails: {
                getPricedetails: PRICE_MANAGEMENT_ROOT_CONTEXT + "price-details-for-product",
                addPriceDetails: PRICE_MANAGEMENT_ROOT_CONTEXT + "add-price-details",
                bulkUploadPrices: PRICE_MANAGEMENT_ROOT_CONTEXT + "add-price-details-in-bulk",
                updatePriceDetails: PRICE_MANAGEMENT_ROOT_CONTEXT + "update-price-details",
                getAllPricedetailsForUnit: PRICE_MANAGEMENT_ROOT_CONTEXT + "price-details-for-unit"
            },
            warehouseClosing: {
                getSkuIdsForUnit: WH_STOCK_MANAGEMENT_CONTEXT + "get-sku-for-unit",
                dayClose: WH_STOCK_MANAGEMENT_CONTEXT + "day-close",
                getRegularOrderingEvents: WH_STOCK_MANAGEMENT_CONTEXT + "get-regular-ordering-events",
                setlleRejectedGR: WH_STOCK_MANAGEMENT_CONTEXT + "settle-pending-rejectedGR",
                cancelDayClose: WH_STOCK_MANAGEMENT_CONTEXT + "day-close/cancel",
                receivings: WH_STOCK_MANAGEMENT_CONTEXT + "receivings",
                transfers: WH_STOCK_MANAGEMENT_CONTEXT + "transfers",
                gatepasses: WH_STOCK_MANAGEMENT_CONTEXT + "gatepasses",
                returns: WH_STOCK_MANAGEMENT_CONTEXT + "returns",
                invoices: WH_STOCK_MANAGEMENT_CONTEXT + "invoices",
                bookings: WH_STOCK_MANAGEMENT_CONTEXT + "bookings-summary",
                reverseBookings: WH_STOCK_MANAGEMENT_CONTEXT + "reverse-bookings-summary",
                wastages: WH_STOCK_MANAGEMENT_CONTEXT + "wastages",
                inventoryLists: WH_STOCK_MANAGEMENT_CONTEXT + "inventory",
                ackTrxns: WH_STOCK_MANAGEMENT_CONTEXT + "ack-transactions",
                submitInventory: WH_STOCK_MANAGEMENT_CONTEXT + "submit-inventory",
                correctInventory: WH_STOCK_MANAGEMENT_CONTEXT + "correct-inventory",
                submitDayClose: WH_STOCK_MANAGEMENT_CONTEXT + "submit-day-close",
                checkDayCloseThreshold: WH_STOCK_MANAGEMENT_CONTEXT + "day-close-threshold",
                checkFixedAssetDayClose: WH_STOCK_MANAGEMENT_CONTEXT + "fa-day-close",
                stockAtHand: WH_STOCK_MANAGEMENT_CONTEXT + "stock-at-hand",
                downloadList: WH_STOCK_MANAGEMENT_CONTEXT + "download-inventory-list",
                closingDates: WH_STOCK_MANAGEMENT_CONTEXT + "closing-dates",
                previewNegativeStocks : WH_STOCK_MANAGEMENT_CONTEXT + "preview-variance",
                kWhAutoDayClose : WH_STOCK_MANAGEMENT_CONTEXT + "k-wh-auto-day-close-for-unit",
                uploadStockSheet : WH_STOCK_MANAGEMENT_CONTEXT + "upload-sku",
            },
            manualBillBookManagement: {
                validateManualBillBook: KETTLE_METADATA_MANAGEMENT_ROOT_CONTEXT + "validate-manual-bill-book",
                createManualBillBookEntry: KETTLE_METADATA_MANAGEMENT_ROOT_CONTEXT + "create-manual-bill-book",
                cancelManualBillBookEntry: KETTLE_METADATA_MANAGEMENT_ROOT_CONTEXT + "cancel-manual-bill-book"
            },
            paymentRequestManagement: {
                paymentRequest: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-request",
                validatePrForQuery: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "validate-pr-for-query",
                uploadQueryDocument: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "upload-query-document",
                rejectedpaymentRequest: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "gr-for-rejected-payment-request",
                getPaymentRequests: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-requests",
                getCompanyPaymentRequest: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-requests-company",
                getCompanyPaymentRequestForProcess: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-requests-company-for-process",
                getVendorPRSummary: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "get-vendor-pr-summary",
                paymentRequestStatusChange: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-request-change-status",
                paymentRequestUpdateInvoice: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-request-update-invoice",
                addDebitNote: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "add-debit-note",
                approve: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "approve",
                reject: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "reject",
                uploadInvoice: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "upload-invoice",
                uploadDebitNoteDoc: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "upload-debit-note",
                getInvoice: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "get-invoice",
                getDebitNotes: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "debit-notes",
                settleDebitNote: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "debit-note-settle",
                paymentDates: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-dates",
                paymentSheet: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-sheet",
                paymentSheetAdhoc: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-sheet-ad-hoc",
                payAdhoc: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-request-pay-adhoc",
                blockPaymentRequests: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-requests-block",
                unblockPaymentRequests: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-requests-unblock",
                uploadPaymentsSheet: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "upload-payment-sheet",
                paymentRequestsSettle: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-requests-settle",
                settlePaymentRequestSingle: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-request-settle",
                paymentRequestForceSettle: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "payment-request-force-settle",
                banksOfCompany: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "banks-of-company",
                updateFilingNumber: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "update-filing-no",
                addHoliday: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "add-holiday",
                listOfHolidays: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "list-of-holidays",
                getMandatoryReqDoc: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "get-mandatory-docs",
                activateOrDeactivateHoliday: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "activate-or-deactivate-holiday",
                getVendorAdvancePayment: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "get-vendor-advance-payment",
                createVendorAdvancePayment: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "create-vendor-advance-payment",
                getAllVendorAdvances: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "get-all-vendor-advances",
                getPosForAdvance: PURCHASE_MANAGEMENT_ROOT_CONTEXT + "get-pos-for-advance",
                submitRefundedDate: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "submit-refunded-date",
                getSosForAdvance: SERVICE_MANAGEMENT_ROOT_CONTEXT + "get-sos-for-advance",
                submitAdjustmentRefund: SERVICE_MANAGEMENT_ROOT_CONTEXT + "submit-adjustment-refund",
                approveRejectAdjustmentRefund: SERVICE_MANAGEMENT_ROOT_CONTEXT + "approve-reject-adjustment-refund",
                uploadAdvanceRefund: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "upload-advance-refund",
                employeePaymentCards: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "employee-payment-cards",
                uploadCardPaymentProof: PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "upload-card-payment-proof",
                uploadSoBreachApproval : PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT + "upload-so-breach-approval"
            },
            genericResourceManagement: {
                uploadDocumentGeneric: GENERIC_RESOURCE_MANAGEMENT_ROOT_CONTEXT + "upload-document-generic"
            },
            recipeManagement: {
                getLinkedRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "find-recipe-containing-productId",
                recipeProfiles: RECIPE_SERVICES_ROOT_CONTEXT + "recipe/profiles",
            },
            accessControl: {
                generateApiToken: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "generate-api-token"
            },
            filter: {
                availableUnits: DATA_FILTER_ROOT_CONTEXT + 'units',
                availableProducts: DATA_FILTER_ROOT_CONTEXT + 'products',
                availableSKUs: DATA_FILTER_ROOT_CONTEXT + 'skus',
                availableProdfromUnitToSku : DATA_FILTER_ROOT_CONTEXT + 'unitSkuProducts'
            },
            transportManagement: {
                transportModes: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "transport-mode",
                vehicles: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "vehicles",
                vehiclesByTransportMode: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "vehicles/mode",
                dispatch: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "dispatch",
                searchDispatch: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "dispatch/search",
                createDispatch: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "dispatch/create",
                pendingTOs: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "pending-transfer-orders",
                createConsignment: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "consignment/create",
                cancelConsignment: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "consignment/cancel",
                startDispatch: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "dispatch/eway/start",
                downloadEwayFile: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "dispatch/eway/download/json",
                uploadEwayFile: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "dispatch/eway/parse",
                updateEwayBillNumber: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "dispatch/eway/update",
                getDispatchDetail: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "dispatch/download",
                getEwayBillDetails: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "dispatch/eway/fetch",
                dispatchHistory: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "dispatch/history",
                getSuggestedUnits: TRANSPORT_MANAGEMENT_ROOT_CONTEXT + "get-suggested-units"

            },
            gatepassManagement: {
                vendorList: GATEPASS_MANAGEMENT_ROOT_CONTEXT + "vendor-list",
                gatepass: GATEPASS_MANAGEMENT_ROOT_CONTEXT + "create-gatepass",
                searchGatepass: GATEPASS_MANAGEMENT_ROOT_CONTEXT + "search-gatepass",
                gatepassDetails: GATEPASS_MANAGEMENT_ROOT_CONTEXT + "gatepass-details",
                updateGatepass: GATEPASS_MANAGEMENT_ROOT_CONTEXT + "update-gatepass",
                cancelGatepass: GATEPASS_MANAGEMENT_ROOT_CONTEXT + "cancel-gatepass",
                addVendorMapping: GATEPASS_MANAGEMENT_ROOT_CONTEXT + "add-vendor-mapping",
                activateVendor: GATEPASS_MANAGEMENT_ROOT_CONTEXT + "vendor-activate",
                deactivateVendor: GATEPASS_MANAGEMENT_ROOT_CONTEXT + "vendor-deactivate"
            },
            invoiceManagement: {
                createRequest: INVOICE_MANAGEMENT_CONTEXT + "create-invoice-request",
                updateUploadDocumentID: INVOICE_MANAGEMENT_CONTEXT +"update-uploadDocId-invoice",
                getInvoices: INVOICE_MANAGEMENT_CONTEXT + "get-invoices",
                uploadInvoice: INVOICE_MANAGEMENT_CONTEXT + "upload-invoice",
                uploadInvoicesSheet: INVOICE_MANAGEMENT_CONTEXT + "upload-invoice-sheet",
                dispatch: INVOICE_MANAGEMENT_CONTEXT + "dispatch",
                uploadCancelInvoice: INVOICE_MANAGEMENT_CONTEXT + "upload-invoice-cancel",
                approve: INVOICE_MANAGEMENT_CONTEXT + "approve-invoice-request",
                reject: INVOICE_MANAGEMENT_CONTEXT + "reject-invoice-request",
                readyToDispatch: INVOICE_MANAGEMENT_CONTEXT + "ready-to-dispatch",
                cancel: INVOICE_MANAGEMENT_CONTEXT + "cancel",
                closedInvoices: INVOICE_MANAGEMENT_CONTEXT + "closed-invoices",
                uploadPurchaseOrder: INVOICE_MANAGEMENT_CONTEXT + "upload-po",
                downloadExcel: INVOICE_MANAGEMENT_CONTEXT + "download-excel",
                downloadCnDnExcel: INVOICE_MANAGEMENT_CONTEXT + "download-cn-dn-excel",
                downloadJson: INVOICE_MANAGEMENT_CONTEXT + "download-json",
                downloadEportalJson: INVOICE_MANAGEMENT_CONTEXT + "download-Eportal-json",
                downloadCorrectionEportalJson: INVOICE_MANAGEMENT_CONTEXT + "download-correction-Eportal-json",
                getUrl: INVOICE_MANAGEMENT_CONTEXT + "get-url",
                requestOrders: INVOICE_MANAGEMENT_CONTEXT + "request-orders",
                notifyError: INVOICE_MANAGEMENT_CONTEXT + "notify-error",

                generateAndSaveB2bInvoice:INVOICE_MANAGEMENT_CONTEXT + "generate-and-save-b2b-invoice",
                generateAndSaveSpecializedOrderInvoice:INVOICE_MANAGEMENT_CONTEXT + "generate-and-save-specialized-order-invoice",
                saveGrPrDeviations : INVOICE_MANAGEMENT_CONTEXT + "save-special-gr-pr-diff",
                saveOutwardRegisterEntry:INVOICE_MANAGEMENT_CONTEXT +"save-outward-register-entry",
                getOutWardFormEntries:INVOICE_MANAGEMENT_CONTEXT +"get-outward-form-entries",
                approveCancellation: INVOICE_MANAGEMENT_CONTEXT + "approve-cancellation",
                saveCorrectedInvoiceDetails: INVOICE_MANAGEMENT_CONTEXT + "save-corrected-invoice-details",
                getCorrectedInvoiceDetails: INVOICE_MANAGEMENT_CONTEXT + "get-corrected-invoice-details",
                saveCreditDebitNoteDetails: INVOICE_MANAGEMENT_CONTEXT + "save-credit-debit-note-details",
                getCreditDebitNoteDetails: INVOICE_MANAGEMENT_CONTEXT + "get-credit-debit-note-details",
                approveCreditNote : INVOICE_MANAGEMENT_CONTEXT + "approve-credit-note",
                rejectCreditNote : INVOICE_MANAGEMENT_CONTEXT + "reject-credit-note",
                uploadVendorInvoice : INVOICE_MANAGEMENT_CONTEXT + "upload-vendor-invoice",
                getCreditNoteDetail : INVOICE_MANAGEMENT_CONTEXT + "get-credit-note-detail",
                generateCreditNote : INVOICE_MANAGEMENT_CONTEXT + "generate-credit-note",
                generateCorrectionCreditNote : INVOICE_MANAGEMENT_CONTEXT + "generate-correction-credit-note",
                generateDebitNote : INVOICE_MANAGEMENT_CONTEXT + "generate-debit-note",
                generateCorrectionDebitNote : INVOICE_MANAGEMENT_CONTEXT + "generate-correction-debit-note",
                uploadCorrectionInvoiceSheet: INVOICE_MANAGEMENT_CONTEXT + "upload-correction-invoice-sheet",
                validateAndClose: INVOICE_MANAGEMENT_CONTEXT + "validate-and-close",
                getGstTypes: INVOICE_MANAGEMENT_CONTEXT + "get-gst-types",

            },
            scmReportManagement:{
                generateReport : AUTOMATED_SCM_REPORTS_ROOT_CONTEXT + "generate-scm-report"
          },
            ldcVendorManagement : {
                    getLdcForVendor :  LDC_VENDOR_ROOT_CONTEXT + "get-ldc-data",
                    addLdcData : LDC_VENDOR_ROOT_CONTEXT + "add-ldc-data",
                    deactivateLdcData : LDC_VENDOR_ROOT_CONTEXT + "deactivate-ldc-data",
                    updateLdcData : LDC_VENDOR_ROOT_CONTEXT + "update-ldc-data"
            }
        };
        return service;
    }

})();
