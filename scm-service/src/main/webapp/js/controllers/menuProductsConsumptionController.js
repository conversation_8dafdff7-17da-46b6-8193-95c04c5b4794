angular.module('scmApp')
    .controller('menuProductsConsumptionController', ['$rootScope', '$scope', '$stateParams', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'metaDataService', '$alertService', '$timeout', 'recipeService', 'toast', '$q',
        function ($rootScope, $scope, $stateParams, apiJson, $http, appUtil, $toastService, $state, metaDataService, $alertService, $timeout, recipeService, toast, $q) {

            $scope.init = function () {
                console.log("Menu Products Consumption Controller Initialized");
                $scope.scmProductDetailsProductMap = {};
                angular.forEach(appUtil.getScmProductDetails(), function (prod) {
                    $scope.scmProductDetailsProductMap[prod.productId] = prod;
                });
                $scope.selectedBrandDetails = null;
                $scope.fulfillmentDate = null;
                $scope.noOfDays = 2;
                $scope.getFulfilmentUnits();
                $scope.raiseBy=false;
                $scope.dataEntry = [];
                $scope.showProductsList = false;
                $scope.productConsumptionList = [];
                $scope.remainingDays = [];
                $scope.orderingDays = [];

                // SCM Products variables
                $scope.showScmProductsList = false;
                $scope.scmProductConsumptionList = [];

                // Packaging Products variables (like refOrderCreateV2)
                $scope.showPackagingProductsList = false;
                $scope.packagingProductList = [];
                metaDataService.getUnitProductData(function (unit) {
                    $scope.unitData = unit;
                    $scope.scmProductDetails = appUtil.getScmProductDetails();
                    $scope.menuCategories = recipeService.createMenuItemCategories();
                });

                // Data Tracking variables
                $scope.originalMenuProductsData = [];
                $scope.modifiedMenuProductsData = [];
                $scope.orderingPercentages = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];

                // Date-wise SCM Products variables
                $scope.showScmProductsDateWise = false;
                $scope.scmProductsDateWiseList = [];
                $scope.originalScmProductsData =[];

                // Date-wise SCM Packaging variables
                $scope.showScmPackagingListDateWise = false;
                $scope.scmPackagingProductListDateWise = [];
                $scope.scmPackagingCategoriesDateWise = [];

                // Separate arrays for Kitchen and Warehouse
                $scope.kitchenProductsDateWise = [];
                $scope.warehouseProductsDateWise = [];
                $scope.showWarehouseProducts = false; // Checkbox state
                $scope.showSpecializedRoProducts = false; // Checkbox state

                $scope.brandList = appUtil.getBrandList();
                $scope.setDates(appUtil.getDate(2), $scope.noOfDays);
                $scope.comment = null;
                $scope.minRefOrderFulfillmentDate = appUtil.getDate(1);
                $scope.maxRefOrderFulfillmentDate = appUtil.getDate(7);
                console.log($scope.minRefOrderFulfillmentDate + "," + $scope.maxRefOrderFulfillmentDate);
                $scope.brandDetails = [{id: 1, brandName: "CHAAYOS"},
                    {id: 3, brandName: "GNT"},
                    {id: 6, brandName: "DOHFUL"},
                    {id: 0, brandName: "CHAAYOS_AND_GNT"}];
                $scope.selectedBrandDetails = $scope.brandDetails[0];
                // Get strategy info from rootScope (set by MenuController)
                $scope.suggestiveOrderingStrategy = $rootScope.suggestiveOrderingStrategy;

                // Check strategy type to determine if expiry logic is needed
                if ($scope.suggestiveOrderingStrategy && $scope.suggestiveOrderingStrategy.strategyType) {
                    $scope.hasExpiryLogic = $scope.suggestiveOrderingStrategy.strategyType === 'GM_WITH_EXPIRY';
                } else {
                    $scope.hasExpiryLogic = false;
                }

                // Initialize productMap like suggestive ordering (CRITICAL for recipeService)
                $scope.productList = appUtil.getScmProductDetails();
                $scope.productMap = {};
                angular.forEach(appUtil.getScmProductDetails(), function (prod) {
                    $scope.productMap[prod.productId] = prod;
                });
                console.log("ProductMap initialized with", Object.keys($scope.productMap).length, "products");

                // Expiry related variables (similar to suggestive ordering)
                if ($scope.hasExpiryLogic) {
                    $scope.expiryDataCheck = false;
                    $scope.dayWiseExpiryProduct = {};
                    $scope.totalDayWiseExpiry = {};
                    $scope.acknowledgedRoDayWiseExpiry = {};
                    $scope.copyOfTotalStock = {};
                    $scope.productWiseFulfilment = {};
                    console.log("Expiry logic variables initialized for GM_WITH_EXPIRY strategy");
                } else {
                    console.log("No expiry logic needed for strategy type:", $scope.suggestiveOrderingStrategy ? $scope.suggestiveOrderingStrategy.strategyType : 'None');
                }

                // Category-wise grouping variables for Menu Products
                $scope.menuProductCategories = [];
                $scope.filteredMenuProductCategories = []; // Filtered categories based on search
                $scope.categoryDateMultipliers = {}; // Store multiplier for each category + date combination
                $scope.availableMultipliers = [100, 10, 25, 50, 75, 125, 150, 200]; // Available multiplier percentages

                // Search functionality for Menu Products
                $scope.searchText = "";
                $scope.originalMenuProductCategories = []; // Store original categories for filtering

                // Search functionality for SCM Products
                $scope.scmSearchText = "";
                $scope.filteredKitchenProductsDateWise = []; // Filtered kitchen products based on search
                $scope.filteredWarehouseProductsDateWise = []; // Filtered warehouse products based on search
                $scope.originalKitchenProductsDateWise = []; // Store original kitchen products for filtering
                $scope.originalWarehouseProductsDateWise = []; // Store original warehouse products for filtering

                // Hide loader after initialization
                $timeout(function() {
                    $rootScope.showSpinner = false;
                }, 100);
            };

             $scope.setBrand = function (brand) {
                $scope.selectedBrandDetails = brand;
                $scope.setDates($scope.fulfillmentDate, 2);
             };
             function makeDateString(date) {
                 var newDate = new Date(date);
                 var result = newDate.getFullYear() + "-" + (newDate.getMonth() + 1) + "-" + (newDate.getDate());
                 console.log("result Date is : ", result);
                 return result;
             }
             function weekDays() {
                 var days = [
                     {id: 1, value: 'Sunday'},
                     {id: 2, value: 'Monday'},
                     {id: 3, value: 'Tuesday'},
                     {id: 4, value: 'Wednesday'},
                     {id: 5, value: 'Thursday'},
                     {id: 6, value: 'Friday'},
                     {id: 7, value: 'Saturday'}];
                 return days;
             }
            $scope.getDayOfWeek = getDayOfWeek;
            function getDayOfWeek(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                console.log('Selected Date', date.getDay());
                return weekDays()[date.getDay()].value;
            }

            $scope.preventWheel = function(event) {
                event.preventDefault();
            };

            $scope.preventDecimal = function(event) {
                if (event.key === '.' || event.key === 'e' || event.key === '-'
                    || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
                    event.preventDefault();
                }
                if (!/^\d$/.test(event.key)) {
                        event.preventDefault();
                    }
            };

            $scope.preventInvalidDecimal = function(event) {
                if (event.key === 'e' || event.key === '-'
                    || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
                    event.preventDefault();
                }

                var input = event.target.value;
                var key = event.key;

                // Allow digits
                if (/^\d$/.test(key)) {
                    // Check if decimal digits are already 6, prevent further if so
                    var decimalPos = input.indexOf('.');
                    if (decimalPos >= 0) {
                        var decimals = input.substring(decimalPos + 1);
                        // Allow digits only if decimals < 6 or cursor before decimal point
                        var cursorPos = event.target.selectionStart;
                        if (cursorPos > decimalPos && decimals.length >= 6) {
                            event.preventDefault();
                        }
                    }
                    return;
                }

                // Allow only one dot
                if (key === '.') {
                    if (input.indexOf('.') !== -1) {
                        event.preventDefault();
                    }
                    return;
                }

                // Prevent all other keys
                event.preventDefault();
            };


            var debounceTimer = null;
            $scope.isApiInProgress = false;
            $scope.setDates = function (fulfillmentDate, noOfDays) {
                if (debounceTimer) {
                    $timeout.cancel(debounceTimer);
                }
                debounceTimer = $timeout(function () {
                   try {
                         if ($scope.isApiInProgress) return;
                         $scope.isApiInProgress = true;
                         $scope.dataEntry = [];
                         $scope.remainingDays = [];
                         $scope.orderingDays = [];
                         $scope.onlyOrderingDays = [];
                         $scope.OrderingDaysFinal = [];
                         $scope.remainingDaysFinal = [];
                         var brandName = $scope.selectedBrandDetails.brandName;
                         var isDohful = brandName === 'DOHFUL';
                         var maxAllowedDays = isDohful ? 7 : 3;

                         if (!noOfDays || isNaN(noOfDays) || noOfDays <= 0) {
                            $toastService.create("Please Enter Ordering Days..!");
                            $scope.noOfDays = 0;
                            $scope.isApiInProgress = false;
                            return;
                         }

                         if (noOfDays > maxAllowedDays) {
                            $toastService.create("Ordering Days cannot be more than " + maxAllowedDays + "  for selected brand.");
                            console.log("maxAllowedDays, days is : ", maxAllowedDays, noOfDays);
                            $scope.noOfDays = 0;
                            $scope.isApiInProgress = false;
                            return;
                         }

                         $scope.fulfillmentDate = $scope.regularOrderingEvent != null ? makeDateString($scope.regularOrderingEvent.fulfilmentDate) : makeDateString(fulfillmentDate);
                         var regularOrderingDate = appUtil.getRegularOrderingDate();
                         $scope.noOfRemainingDays = appUtil.datediffRO(regularOrderingDate, $scope.fulfillmentDate);
                         $scope.noOfDays = $scope.regularOrderingEvent != null ? $scope.regularOrderingEvent.orderingDays : noOfDays;
                         $scope.stockLastingDate = appUtil.calculatedDate($scope.noOfDays - 1, $scope.fulfillmentDate);
                         $scope.stockLastingDay = $scope.getDayOfWeek($scope.stockLastingDate);
                         if (new Date($scope.stockLastingDate) > new Date($scope.maxRefOrderFulfillmentDate)) {
                            $toastService.create(" Ordering days go outside the allowed window (from " + appUtil.formatDate($scope.minRefOrderFulfillmentDate, 'yyyy-MM-dd') + " to " + appUtil.formatDate($scope.maxRefOrderFulfillmentDate, 'yyyy-MM-dd') + ")");
                            $scope.noOfDays = 0;
                            $scope.stockLastingDate = null;
                            $scope.stockLastingDay = null;
                            $scope.isApiInProgress = false;
                            return;
                         }
                         // Create data entry for days
                         for (var i = 1; i <= $scope.noOfRemainingDays - 1; i++) {
                             $scope.dataEntry.push({
                                 dayType: 'REMAINING_DAY',
                                 date: appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"),
                                 brands: [{
                                     id: $scope.chaayosId,
                                     saleAmount: 0,
                                     deliverySalePercentage: 0
                                 },
                                     {
                                         id: $scope.gntId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     },
                                     {
                                         id: $scope.dcId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     }],
                                 orderingPercentage : 100

                             })
                             $scope.OrderingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                             $scope.remainingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                             $scope.remainingDays.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                         }
                         for (var i = 0; i <= $scope.noOfDays - 1; i++) {
                             $scope.dataEntry.push({
                                 dayType: 'ORDERING_DAY',
                                 date: appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"),
                                 brands: [{
                                     id: $scope.chaayosId,
                                     saleAmount: 0,
                                     deliverySalePercentage: 0
                                 },
                                     {
                                         id: $scope.gntId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     },
                                     {
                                         id: $scope.dcId,
                                         saleAmount: 0,
                                         deliverySalePercentage: 0
                                     }],
                                 orderingPercentage : 100
                             })
                             $scope.OrderingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"));
                             $scope.orderingDays.push(appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"));
                         }
                         console.log("data entry is :", $scope.dataEntry);

                         // Fetch expiry data if expiry logic is enabled
                         if ($scope.orderingDays.length > 0) {
                             console.log("Fetching expiry data for strategy type:", $scope.suggestiveOrderingStrategy.strategyType);
                             $scope.getDayWiseExpiryProduct().finally(function() {
                                 $scope.isApiInProgress = false;
                             });
                         } else {
                             $scope.isApiInProgress = false;
                         }
                   } catch (e) {
                     console.log("Error in setDates: ", e);
                   } finally {
                     $timeout(function() {
                         $scope.isApiInProgress = false;
                     });
                   }
                }, 500);
            };

            $scope.getMenuProductsConsumption = function () {
                if (!$scope.selectedBrandDetails || !$scope.fulfillmentDate || !$scope.noOfDays) {
                    $toastService.create("Please select all required fields!");
                    return;
                }

                // Check if expiry data is required and loaded
                if (!$scope.expiryDataCheck) {
                    $toastService.create("Wait for expiry data to load..!");
                    return;
                }

                $alertService.alert("Please Wait..!", "<b>Fetching menu products consumption data...</b>", function (result) {
                }, false);

                var dayList = $scope.dataEntry.map(function(entry) {
                    return entry.date;
                });

                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    dayList: dayList,
                    brandName: $scope.selectedBrandDetails.brandName,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    requestedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.getMenuProductsConsumptionAverage,
                    data: inputData,
                }).then(function success(response) {
                    if (response.data != null) {
                        $toastService.create("Menu products consumption data fetched successfully!");
                        console.log("Menu Products Consumption Data:", response.data);
                        processMenuProductsConsumptionData(response.data);
                        $scope.showProductsList = true;
                        $alertService.closeAlert();
                    } else {
                        $toastService.create("No data found for the selected criteria!");
                        $alertService.closeAlert();
                    }
                }, function error(response) {
                    console.log("Error:", response);
                    $toastService.create("Error fetching consumption data!");
                    $alertService.closeAlert();
                });
            };

            function processMenuProductsConsumptionData(consumptionMap) {
                $scope.productConsumptionList = [];

                var productMap = {};

                // Loop through each date in the map
                Object.keys(consumptionMap).forEach(function(dateKey) {
                    var productsList = consumptionMap[dateKey];

                    var formattedDate = $scope.dateFormatting(dateKey);
                    var isOrderingDay = $scope.orderingDays.includes(formattedDate);

                    productsList.forEach(function(product) {
                        var productKey = product.productId + "_" + product.dimension;

                        if (!productMap[productKey]) {
                            productMap[productKey] = {
                                productId: product.productId,
                                productName: product.productName,
                                dimension: product.dimension,
                                productCategory: product.productCategory || "Others", // Add category
                                remainingDaysData: {},
                                orderingDaysData: {},
                                finalQuantity: 0,
                                maxBiasQuantity: product.maxBiasQuantity
                            };
                        }

                        var quanObj ={
                            quantity:  Math.max(product.averageSalesQuantity + product.maxBiasQuantity, 0) || 0,
                            originalQuantity: product.averageSalesQuantity || 0
                        }
                        if (isOrderingDay) {
                            productMap[productKey].orderingDaysData[formattedDate] = quanObj;
                        } else {
                            productMap[productKey].remainingDaysData[formattedDate] = product.averageSalesQuantity || 0;
                        }
                    });

                });
                console.log("Product Map:", productMap);

                // Convert to array and calculate final quantities
                Object.keys(productMap).forEach(function(key) {
                    var product = productMap[key];
                    $scope.calculateFinalQuantity(product);
                    $scope.productConsumptionList.push(product);
                });

                // Store original data (deep copy)
                $scope.originalMenuProductsData = JSON.parse(JSON.stringify($scope.productConsumptionList));

                console.log("Original Menu Products Data Stored:", $scope.originalMenuProductsData);

                // Group products by category and initialize multipliers
                groupProductsByCategory();
            }

            $scope.safeInteger = function(value) {
                if (value === undefined || value === null || isNaN(value)) {
                    return 0;
                }
                return value;
            }

            $scope.onBlurSetZero = function(product, date) {
                if (!product.orderingDaysData[date].quantity && product.orderingDaysData[date].quantity !== 0) {
                    product.orderingDaysData[date].quantity = 0;
                }
            };

            $scope.increaseQuantity = function(product, date) {
                var current = parseInt(product.orderingDaysData[date].quantity) || 0;
                var originalQuantity = parseInt(product.orderingDaysData[date].originalQuantity) || 0;
                var key = product.productCategory + "_" + date;
                var multiplier = $scope.categoryDateMultipliers[key] / 100;
                var max = 0;
                if(product.maxBiasQuantity<0){
                    max = Math.round(originalQuantity*multiplier);
                }
                else {
                    max = Math.round((originalQuantity + 2*product.maxBiasQuantity)*multiplier);
                }

                if (current < max) {
                    product.orderingDaysData[date].quantity = current + 1;
                    $scope.calculateFinalQuantity(product);
                    // Sync changes to modifiedMenuProductsData
                    syncUIChangesToModifiedData();
                }
                else {
                    $toastService.create("Can not increase the quantity more than: "+ max + " for current date: "+ date);
                }
            };

            $scope.decreaseQuantity = function(product, date) {
                var current = parseInt(product.orderingDaysData[date].quantity) || 0;
                var originalQuantity = parseInt(product.orderingDaysData[date].originalQuantity) || 0;
                var key = product.productCategory + "_" + date;
                var multiplier = $scope.categoryDateMultipliers[key] / 100;
                var min = Math.round(originalQuantity*multiplier); // fallback limit
                if(product.maxBiasQuantity<0){
                    min = 0;
                }
                if (current > min) {
                    product.orderingDaysData[date].quantity = current - 1;
                    $scope.calculateFinalQuantity(product);
                    // Sync changes to modifiedMenuProductsData
                    syncUIChangesToModifiedData();
                }
                else {
                    $toastService.create("Can not decrease the quantity below: "+ min + " for current date: "+ date);
                }
            };

            // Create a reusable debounce function
            function debounce(func, wait) {
                var timeout;
                return function() {
                    var context = this,
                        args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(function() {
                        func.apply(context, args);
                    }, wait);
                };
            }


            // Debounced version
            $scope.debouncedValidateQuantity = debounce(function(product, date) {
                $scope.validateMaxQuantity(product, date, false);
                $scope.calculateFinalQuantity(product);
                // Sync changes to modifiedMenuProductsData
                syncUIChangesToModifiedData();
                $scope.$apply(); // Required to trigger digest cycle
            }, 1000);


            $scope.validateMaxQuantity = function(product, date) {
                var value = parseInt(product.orderingDaysData[date].quantity) || 0;
                var originalQuantity = parseInt(product.orderingDaysData[date].originalQuantity) || 0;
                var key = product.productCategory + "_" + date;
                var multiplier = $scope.categoryDateMultipliers[key] / 100;
                var max = 0;
                var min = 0;
                if(product.maxBiasQuantity<0){
                    max = Math.round(originalQuantity*multiplier);
                    min = 0;
                }
                else {
                    max = Math.round((originalQuantity + 2*product.maxBiasQuantity)*multiplier);
                    min = Math.round(originalQuantity*multiplier);
                }

                var quantityChanged = false;
                if (value > max) {
                    product.orderingDaysData[date].quantity = max; // reset to max
                    $scope.calculateFinalQuantity(product);
                    quantityChanged = true;
                    $toastService.create("Max allowed quantity is " + max + " for date: " + date);
                }

                if (value < min) {
                    product.orderingDaysData[date].quantity = min; // reset to min
                    $scope.calculateFinalQuantity(product);
                    quantityChanged = true;
                    $toastService.create("Min allowed quantity is " + min + " for date: " + date);
                }

                // Sync changes to modifiedMenuProductsData if quantity was changed
                if (quantityChanged) {
                    syncUIChangesToModifiedData();
                }
            };

            $scope.calculateFinalQuantity = function(product) {
                var total = 0;
                // Sum up ordering days quantities
                Object.keys(product.orderingDaysData).forEach(function(dateKey) {
                    var quantity = product.orderingDaysData[dateKey].quantity || 0;
                    total += +quantity;
                });

                product.finalQuantity = total;
            };

            $scope.goBackToSelection = function() {
                $scope.showProductsList = false;
                $scope.productConsumptionList = [];
                $scope.originalMenuProductsData = [];
                $scope.modifiedMenuProductsData = [];
                $scope.categoryDateMultipliers = {};
                $scope.isMultiplierInitialized = {};
                $scope.suppressMultiplierChange = {};
                $scope.previousMultipliers = {};
                $scope.menuProductCategories = [];
                $scope.filteredMenuProductCategories = [];
                $scope.originalMenuProductCategories = [];
                $scope.searchText = "";
            };

            $scope.dateFormatting = function (date) {
                return appUtil.formatDate(new Date(date), "yyyy-MM-dd");
            };

            // Expiry data fetching function (similar to suggestive ordering)
            $scope.getDayWiseExpiryProduct = function () {

                $scope.expiryDataCheck = false;
                console.log("Fetching day-wise expiry data for dates:", $scope.orderingDays);

                return $http({
                    method: 'GET',
                    url: apiJson.urls.stockManagement.getDayWiseExpiryProduct,
                    params: {
                        "unitId": appUtil.getCurrentUser().unitId,
                        "dates": $scope.OrderingDaysFinal,
                        "isAggregated": false,
                        "firstOrderingDate": $scope.orderingDays[0]
                    }
                }).then(function success(response) {
                    console.log("Expiry data response:", response.data);
                    $scope.dayWiseExpiryProduct = response.data;
                    $scope.totalDayWiseExpiry = angular.copy(response.data["totalStock"]);
                    $scope.acknowledgedRoDayWiseExpiry = angular.copy(response.data["acknowledgedRoInTransit"]);
                    $scope.makeAcknowledgedRoDayWiseExpiry(angular.copy(response.data["acknowledgedRoInTransit"]));
                    $scope.copyOfTotalStock = angular.copy($scope.totalDayWiseExpiry);
                    $scope.originalCopyOfTotalStock = angular.copy($scope.totalDayWiseExpiry);
                    $scope.expiryDataCheck = true;

                    console.log("Expiry data loaded successfully");
                }, function error(response) {
                    console.log("Error fetching expiry data:", response);
                    $scope.expiryDataCheck = false;
                    $toastService.create("Error fetching expiry data!");
                });
            };

            $scope.makeAcknowledgedRoDayWiseExpiry = function (acknowledgedStock) {
                $scope.acknowledgedStockMap = {};
                $scope.acknowledgedStockQuantityMap = {};
                if (acknowledgedStock !== undefined && acknowledgedStock != null) {
                    angular.forEach($scope.OrderingDaysFinal, function (date) {
                        if (acknowledgedStock[date] !== undefined && acknowledgedStock[date] != null) {
                            angular.forEach(acknowledgedStock[date], function (productWithExpiries, productId) {
                                var key = productId + "_" + date;
                                $scope.acknowledgedStockMap[key] = productWithExpiries;
                                angular.forEach(productWithExpiries, function (quantity) {
                                     if ($scope.acknowledgedStockQuantityMap[productId] !== undefined && $scope.acknowledgedStockQuantityMap[productId] != null) {
                                         $scope.acknowledgedStockQuantityMap[productId] = quantity + $scope.acknowledgedStockQuantityMap[productId];
                                     } else {
                                         $scope.acknowledgedStockQuantityMap[productId] = quantity;
                                     }
                                });
                            });
                        }
                    });
                }
            };

            function getAllExpiry(item) {
                var msg = "(I,T) - ";
                if ($scope.totalDayWiseExpiry && $scope.totalDayWiseExpiry[item.id] !== undefined && $scope.totalDayWiseExpiry[item.id] != null) {
                    msg+=JSON.stringify($scope.totalDayWiseExpiry[item.id]);
                } else {
                    msg += "NA";
                }
                msg += " , ACK - ";
                if ($scope.totalDayWiseExpiry && $scope.acknowledgedStockQuantityMap[item.id] !== undefined && $scope.acknowledgedStockQuantityMap[item.id] != null) {
                    angular.forEach($scope.OrderingDaysFinal, function (date) {
                        var key = item.id + "_" + date;
                        if ($scope.acknowledgedStockMap[key] !== undefined && $scope.acknowledgedStockMap[key] != null) {
                            msg += " On Date : " + date + " Exp are : " + JSON.stringify($scope.acknowledgedStockMap[key]);
                        }
                    });
                } else {
                    msg += "NA";
                }
                return msg;
            }

            // Rounding utility function
            function roundQuantity(quantity) {
                if (!quantity || quantity === 0) return 0;
                var decimal = quantity - Math.floor(quantity);
                if (decimal < 0.5) {
                    return Math.floor(quantity);
                } else {
                    return Math.ceil(quantity);
                }
            }

            $scope.fetchAllScmData = function () {
                $scope.getScmProductsFromMenuDateWise()
                    .then(function () {
                        return $scope.getScmProductsConsumptionAverage();
                    })
            };

            // DATE-WISE SCM Products Function (New Format)
            $scope.getScmProductsFromMenuDateWise = function() {
                var deferred = $q.defer();

                if ($scope.productConsumptionList.length === 0) {
                    $toastService.create("Please fetch menu products data first!");
                    deferred.reject("Missing product consumption list");
                    return deferred.promise;
                }

                // Sync UI changes to modifiedMenuProductsData before submitting
                syncUIChangesToModifiedData();
                console.log("Modified Menu Products Data Synced before API call:", $scope.modifiedMenuProductsData);

                $alertService.alert("Please Wait..!", "<b>Fetching SCM products from menu (date-wise)...</b>", function (result) {
                }, false);

                // Prepare date-wise menu products data
                var dateWiseMenuData = prepareDateWiseMenuData();
                console.log("Date-wise Menu Data for API:", dateWiseMenuData);

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.getScmProductsFromMenuDateWise,
                    params: {
                        unitId: appUtil.getCurrentUser().unitId
                    },
                    data: dateWiseMenuData
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        console.log("Date-wise SCM Products Response:", response.data);
                        processDateWiseScmProducts(response.data);

                        // Store the product IDs that we got from menu-to-SCM conversion
                        $scope.menuToScmProductIds = [];
                        $scope.scmProductsDateWiseList.forEach(function(scmProduct) {
                            if ($scope.menuToScmProductIds.indexOf(scmProduct.productId) === -1) {
                                $scope.menuToScmProductIds.push(scmProduct.productId);
                            }
                        });
                        console.log("Menu-to-SCM Product IDs:", $scope.menuToScmProductIds);
                        deferred.resolve(); // success

                    } else {
                        $toastService.create("No SCM products found for the selected dates!");
                        $alertService.closeAlert();
                        deferred.reject("No products found");
                    }
                }, function error(response) {
                    console.log("Date-wise SCM Error:", response);
                    $toastService.create("Error fetching date-wise SCM products!");
                    $alertService.closeAlert();
                     deferred.reject("API error");
                });
                return deferred.promise;
            };

            // Get SCM Products Consumption Average (similar to suggestive ordering)
            $scope.getScmProductsConsumptionAverage = function () {
                var deferred = $q.defer();

                $alertService.alert("Please Wait..!", "<b>Fetching additional SCM products consumption data...</b>", function (result) {
                }, false);

                $scope.scmProductsConsumptionAverageMap = {};
                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    fulfillmentDate: $scope.dateFormatting($scope.fulfillmentDate),
                    noOfDays: $scope.noOfDays,
                    brandName: $scope.selectedBrandDetails.brandName,
                    salesData: $scope.dataEntry,
                    requestedBy: appUtil.getCurrentUser().user.name + "[" + appUtil.getCurrentUser().user.id + "]"
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.getScmProductsConsumptionAverage,
                    data: inputData
                }).then(function success(response) {
                    $scope.scmProductsConsumptionAverageMap = response.data;
                    console.log("SCM Products Consumption Average Response:", response.data);

                    // Filter out products that were already received from menu-to-SCM conversion
                    var filteredScmConsumption = filterAdditionalScmProducts(response.data);
                    console.log("Filtered Additional SCM Products:", filteredScmConsumption);

                    // Merge filtered SCM products with existing date-wise SCM products
                    if (Object.keys(filteredScmConsumption).length > 0) {
                        mergeAdditionalScmProducts(filteredScmConsumption);
                        console.log("Merged SCM Products List:", $scope.scmProductsDateWiseList);
                    }

//                     Apply expiry logic if enabled before packaging
                    if ($scope.hasExpiryLogic && $scope.expiryDataCheck) {
                        console.log("Applying expiry logic to SCM products...");
                        applyExpiryLogicToScmProducts();
                    }

                    $scope.getProductWiseStock();

                    $scope.submitScmProductsForPackagingDateWise();

                    // Directly call packaging logic after successful SCM data fetch
                    $alertService.alert("Please Wait..!", "<b>Processing packaging logic...</b>", function (result) {
                    }, false);

                    var successMessage = $scope.hasExpiryLogic ?
                        "SCM products fetched with expiry logic and packaging processed successfully!" :
                        "SCM products fetched and packaging processed successfully!";
                    $toastService.create(successMessage);
                    deferred.resolve(); // success

                }, function error(response) {
                    console.log("Error in getScmProductsConsumptionAverage:", response);
                    $scope.scmProductsConsumptionAverageMap = {};
                    if (response.data && response.data.errorMsg != null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                    } else {
                        $toastService.create("Something went wrong while getting additional SCM products consumption");
                    }
                    deferred.reject("Failed to get SCM consumption average");
                });
                return deferred.promise;
            };

            // Filter SCM products that were NOT received from menu-to-SCM conversion
            function filterAdditionalScmProducts(scmConsumptionData) {
                var filteredData = {};

                angular.forEach(scmConsumptionData, function (dayListProducts, date) {
                    var filteredProducts = [];

                    angular.forEach(dayListProducts, function (scmProduct) {
                        // Only include products that were NOT in the menu-to-SCM conversion
                        if ($scope.menuToScmProductIds.indexOf(scmProduct.productId) === -1) {
                            filteredProducts.push(scmProduct);
                        }
                    });

                    if (filteredProducts.length > 0) {
                        filteredData[date] = filteredProducts;
                    }
                });

                return filteredData;
            }

            // Merge additional SCM products with existing date-wise SCM products
            function mergeAdditionalScmProducts(additionalScmData) {
                var additionalScmProductMap = {};

                // Process additional SCM data similar to processDateWiseScmProducts
                angular.forEach(additionalScmData, function (dayListProducts, date) {
                    angular.forEach(dayListProducts, function (scmProduct) {
                        var productKey = scmProduct.productId + "_" + (scmProduct.dimension || "None");
                        var originalScmProductName = "";
                        var originalScmProductUom = "";
                        if($scope.scmProductDetailsProductMap[scmProduct.productId]){
                            originalScmProductName = $scope.scmProductDetailsProductMap[scmProduct.productId].productName;
                            originalScmProductUom = $scope.scmProductDetailsProductMap[scmProduct.productId].uom;
                        }else{
                            originalScmProductName = scmProduct.name;
                            originalScmProductUom = scmProduct.uom;
                        }

                        if (!additionalScmProductMap[productKey]) {
                            additionalScmProductMap[productKey] = {
                                productId: scmProduct.productId,
                                productName: originalScmProductName,
                                dimension: scmProduct.dimension || "None",
                                uom: originalScmProductUom,
                                remainingDaysData: {},
                                orderingDaysData: {},
                                finalQuantity: 0
                            };

                            // Initialize all dates with 0
                            $scope.remainingDays.forEach(function(remDate) {
                                var remDateString = $scope.dateFormatting(remDate);
                                additionalScmProductMap[productKey].remainingDaysData[remDateString] = 0;
                            });

                            $scope.orderingDays.forEach(function(ordDate) {
                                var ordDateString = $scope.dateFormatting(ordDate);
                                additionalScmProductMap[productKey].orderingDaysData[ordDateString] = 0;
                            });
                        }

                        // Set the consumption for this date
                        var dateString = $scope.dateFormatting(date);
                        var quantity = parseFloat(scmProduct.averageConsumption) || 0;

                        if ($scope.remainingDays.some(function(d) {
                            return $scope.dateFormatting(d) === dateString;
                        })) {
                            additionalScmProductMap[productKey].remainingDaysData[dateString] = quantity;
                        } else if ($scope.orderingDays.some(function(d) {
                            return $scope.dateFormatting(d) === dateString;
                        })) {
                            additionalScmProductMap[productKey].orderingDaysData[dateString] = quantity;
                        }

                    });
                });

                // Calculate final quantities and add to main list
                Object.keys(additionalScmProductMap).forEach(function(productKey) {
                    var scmProduct = additionalScmProductMap[productKey];

                    // Calculate final quantity (sum of all ordering days)
                    var totalQuantity = 0;
                    Object.keys(scmProduct.orderingDaysData).forEach(function(date) {
                        totalQuantity += parseFloat(scmProduct.orderingDaysData[date]) || 0;
                    });
                    scmProduct.finalQuantity = totalQuantity;

                    // Add to main SCM products list
                    $scope.scmProductsDateWiseList.push(scmProduct);
                });

                $scope.originalScmProductsData = JSON.parse(JSON.stringify($scope.scmProductsDateWiseList));
                console.log("Original Date-wise SCM Products Data Stored:", $scope.originalScmProductsData);

                console.log("Additional SCM products merged successfully");
            }

            // Prepare date-wise menu products data for API
            function prepareDateWiseMenuData() {
                var dateWiseData = [];

                // Group menu products by date
                var dateProductMap = {};

                // Loop through all dates (remaining + ordering)
                var allDates = $scope.remainingDays.concat($scope.orderingDays);

                allDates.forEach(function(date) {
                    var dateString = $scope.dateFormatting(date);
                    dateProductMap[dateString] = [];

                    // Add products for this date
                    $scope.modifiedMenuProductsData.forEach(function(menuProduct) {
                        var quantity = 0;

                        // Check if this date is in remaining days or ordering days
                        var isOrderingDay = $scope.orderingDays.includes(dateString);
                        var isRemainingDay = $scope.remainingDays.includes(dateString);

                        // Get quantity from appropriate data source
                        if (isOrderingDay && menuProduct.orderingDaysData && menuProduct.orderingDaysData[date]) {
                            quantity = menuProduct.orderingDaysData[date].quantity || 0;
                        } else if (isRemainingDay && menuProduct.remainingDaysData && menuProduct.remainingDaysData[date]) {
                            quantity = menuProduct.remainingDaysData[date] || 0;
                        }

                        // Only add if quantity > 0
                        if (quantity > 0) {
                            dateProductMap[dateString].push({
                                productId: menuProduct.productId,
                                dimension: menuProduct.dimension || "None",
                                quantity: parseFloat(quantity)
                            });
                        }
                    });
                });

                // Convert to required format
                Object.keys(dateProductMap).forEach(function(dateString) {
                    if (dateProductMap[dateString].length > 0) {
                        dateWiseData.push({
                            date: dateString,
                            products: dateProductMap[dateString]
                        });
                    }
                });

                return dateWiseData;
            }

            // Process date-wise SCM products response
            function processDateWiseScmProducts(dateWiseScmData) {
                $scope.scmProductsDateWiseList = [];

                console.log("Processing date-wise SCM data:", dateWiseScmData);

                // Process response data
                var scmProductMap = {};

                dateWiseScmData.forEach(function(dateData) {
                    var dateString = dateData.date;
                    var scmProducts = dateData.products || [];

                    scmProducts.forEach(function(scmProduct) {
                        var scmProductKey = scmProduct.productId + "_" + (scmProduct.dimension || "None");
                        var originalScmProductName = "";
                        if($scope.scmProductDetailsProductMap[scmProduct.productId]){
                            originalScmProductName = $scope.scmProductDetailsProductMap[scmProduct.productId].productName;
                        }else{
                            originalScmProductName = scmProduct.name;
                        }

                        if (!scmProductMap[scmProductKey]) {
                            scmProductMap[scmProductKey] = {
                                productId: scmProduct.productId,
                                productName: originalScmProductName,
                                dimension: scmProduct.dimension || "None",
                                uom: scmProduct.uom || "PC",
                                remainingDaysData: {},
                                orderingDaysData: {},
                                finalQuantity: 0
                            };
                        }

                        // Check if this date is in ordering days or remaining days
                        var isOrderingDay = $scope.orderingDays.includes(dateString);
                        var isRemainingDay = $scope.remainingDays.includes(dateString);

                        if (isOrderingDay) {
                            scmProductMap[scmProductKey].orderingDaysData[dateString] = scmProduct.quantity || 0;
                        } else if (isRemainingDay) {
                            scmProductMap[scmProductKey].remainingDaysData[dateString] = scmProduct.quantity || 0;
                        }
                    });
                });

                console.log("SCM Product Map:", scmProductMap);

                // Convert to array and calculate final quantities
                Object.keys(scmProductMap).forEach(function(key) {
                    var scmProduct = scmProductMap[key];
                    $scope.calculateScmFinalQuantityDateWise(scmProduct);
                    $scope.scmProductsDateWiseList.push(scmProduct);
                });

                console.log("Processed Date-wise SCM Products List:", $scope.scmProductsDateWiseList);
            }

            $scope.calculateScmFinalQuantityDateWise = function(scmProduct) {
                 var total = 0;

                 // Sum up ordering days quantities
                 Object.keys(scmProduct.orderingDaysData).forEach(function(dateKey) {
                    var quantity = scmProduct.orderingDaysData[dateKey] || 0;
                    total += +quantity;
                 });

                 // Apply rounding logic
                 scmProduct.finalQuantity = total;
                 if(scmProduct.conversionRatio>=4 && scmProduct.finalQuantity>scmProduct.conversionRatio){
                    scmProduct.packagingQuantity = roundQuantity(scmProduct.finalQuantity/scmProduct.conversionRatio);
                 }
                 else{
                    scmProduct.packagingQuantity = Math.ceil(scmProduct.finalQuantity/scmProduct.conversionRatio);
                 }
                 scmProduct.orderingQuantity = scmProduct.packagingQuantity * scmProduct.conversionRatio;
            };

            // SUBMIT - GET PACKAGING PRODUCTS for Date-wise SCM
            $scope.submitScmProductsForPackagingDateWise = function() {
                if ($scope.scmProductsDateWiseList.length === 0) {
                    $toastService.create("No SCM products data to submit!");
                    return;
                }

                // Store current user changes
                $scope.modifiedScmProductsMap = {};
                $scope.scmProductsDateWiseList.forEach(function(scmProduct) {
                    var productKey = scmProduct.productId + "_" + (scmProduct.dimension || "None");
                    $scope.modifiedScmProductsMap[productKey] = {
                        finalQuantity: scmProduct.finalQuantity,
                        remainingDaysData: JSON.parse(JSON.stringify(scmProduct.remainingDaysData)),
                        orderingDaysData: JSON.parse(JSON.stringify(scmProduct.orderingDaysData))
                    };
                });

                console.log("Modified SCM Products Map:", $scope.modifiedScmProductsMap);

                $alertService.alert("Please Wait..!", "<b>Processing SCM products for packaging (similar to suggestive ordering)...</b>", function (result) {
                }, false);

                // Prepare data for recipeService (similar to suggestive ordering)
                var scmProductWiseMap = {};
                var totalScmSuggestions = {};
                var productWiseStock = {};
                var productMap = {};
                var fulfilmentUnitProductsMap = {};
                var scmProductDetailsProductMap = $scope.scmProductDetailsProductMap;

                $scope.scmProductsDateWiseList.forEach(function(scmProduct) {
                    var productId = scmProduct.productId;

                    // Map for recipeService (similar to suggestive ordering)
                    scmProductWiseMap[productId] = scmProduct.finalQuantity;
                    totalScmSuggestions[productId] = scmProduct.finalQuantity;

                    // Product map
                    productMap[productId] = {
                        productId: productId,
                        productName: scmProduct.productName,
                        unitOfMeasure: scmProduct.uom,
                        stockAtHand: 0, // Will be fetched from backend
                        inTransit: 0
                    };

                    // Stock map (will be populated by recipeService)
                    productWiseStock[productId] = {
                        stockAtHand: 0,
                        inTransit: 0
                    };
                });

                if ($scope.hasExpiryLogic && $scope.expiryDataCheck) {
                    scmProductWiseMap = angular.copy($scope.scmProductWiseMap);
                    totalScmSuggestions = angular.copy($scope.totalScmSuggestions);
                    scmProductDetailsProductMap = angular.copy($scope.productMap);
                }
                productWiseStock = angular.copy($scope.productWiseStock);
                fulfilmentUnitProductsMap = angular.copy($scope.fulfilmentUnitProductsMap);

                console.log("SCM Product Wise Map:", scmProductWiseMap);
                console.log("Total SCM Suggestions:", totalScmSuggestions);
                console.log("Product Wise Stock:", productWiseStock);
                console.log("Product Map:", productMap);
                console.log("scmProductDetails Products Map:", $scope.scmProductDetailsProductMap);

                // Use recipeService similar to suggestive ordering createSCMOrderingProductList
                recipeService.getScmProductsFromSuggestionsNew(scmProductWiseMap,productWiseStock,totalScmSuggestions,
                    fulfilmentUnitProductsMap, scmProductDetailsProductMap, function (itemList) {
                        console.log("After packaging logic items in SCM Products are:", itemList);

                        // Map packaging data back to original SCM products
                        var packagingDataMap = {};
                        var specializedRoProductsMap = {};
                        itemList.forEach(function (item) {
                            if (item.selectedFulfillmentType != null && item.selectedFulfillmentType !== "EXTERNAL") {
                                packagingDataMap[item.productId] = {
                                    selectedFulfillmentType: item.selectedFulfillmentType,
                                    packagingSize: item.packagingSize || item.conversionRatio || 10,
                                    stockAtHand: item.stockAtHand || 0,
                                    inTransit: item.inTransit || 0,
                                    stock: (item.stockAtHand || 0) + (item.inTransit || 0),
                                    packagingQuantity: item.packagingQuantity,
                                    packagingName: item.packagingName,
                                    orderingQuantity: item.orderingQuantity,
                                    conversionRatio: item.conversionRatio,
                                    packagingId: item.packagingId,
                                    unitOfMeasure: item.unitOfMeasure
                                };
                            }
                             else if (item.supportsSpecialOrdering && item.id !== 100234) {
                                 specializedRoProductsMap[item.productId] = item;
                             }
                        });

                        console.log("Packaging Data Map:", packagingDataMap);
                        console.log("Original SCM Products:", $scope.scmProductsDateWiseList);
                        console.log("Specialized RO Products Map:", specializedRoProductsMap);

                        // Reset arrays
                        $scope.kitchenProductsDateWise = [];
                        $scope.warehouseProductsDateWise = [];
                        $scope.specializedRoProducts = [];

                        // Categorize original SCM products by Kitchen and Warehouse
                        $scope.scmProductsDateWiseList.forEach(function (scmProduct) {
                            var packagingData = packagingDataMap[scmProduct.productId];

                            if (packagingData) {
                                // Add packaging info to original SCM product
                                scmProduct.selectedFulfillmentType = packagingData.selectedFulfillmentType;
                                scmProduct.packagingSize = packagingData.packagingSize || 10;
                                scmProduct.stockAtHand = packagingData.stockAtHand;
                                scmProduct.inTransit = packagingData.inTransit;
                                scmProduct.stock = packagingData.stock;
                                scmProduct.orderingQuantity = packagingData.orderingQuantity;
                                scmProduct.packagingQuantity = packagingData.packagingQuantity;
                                scmProduct.packagingName = packagingData.packagingName;
                                scmProduct.conversionRatio = packagingData.conversionRatio;
                                scmProduct.suggestedQuantity = scmProduct.orderingQuantity;
                                scmProduct.packagingId = packagingData.packagingId;
                                scmProduct.uom = packagingData.unitOfMeasure;

                                // Extract dynamic packaging info from recipeService
                                var recipeItem = itemList.find(function(item) {
                                    return item.productId === scmProduct.productId;
                                });

                                if (recipeItem && recipeItem.moqData) {
                                    // Use MOQ data for dynamic packaging
                                    scmProduct.packagingUnit = recipeItem.moqData.packagingType || 'packets';
                                    scmProduct.packagingSize = recipeItem.moqData.packagingSize || packagingData.packagingSize;
                                    scmProduct.packagingQuantityCalculated = scmProduct.packagingQuantity;

                                } else {
                                    // Fallback to default packaging
                                    scmProduct.packagingUnit = getPackagingUnit(scmProduct.uom);
                                    scmProduct.packagingSize = packagingData.packagingSize;
                                    scmProduct.packagingQuantityCalculated = scmProduct.packagingQuantity;
                                }

                                scmProduct.packagingQuantityCalculated = scmProduct.packagingQuantityCalculated;
                                scmProduct.packagingUnit = packagingData.packagingUnit || 'packet'; // optional
                                if(scmProduct.conversionRatio>=4 && scmProduct.orderingQuantity>scmProduct.conversionRatio){
                                     scmProduct.packagingQuantity = roundQuantity(scmProduct.finalQuantity / scmProduct.conversionRatio);
                                     scmProduct.orderingQuantity = scmProduct.packagingQuantity * scmProduct.conversionRatio;
                                     scmProduct.suggestedQuantity = scmProduct.orderingQuantity;
                                }

                                // Categorize by fulfillment type
                                if (packagingData.selectedFulfillmentType === "KITCHEN") {
                                    $scope.kitchenProductsDateWise.push(scmProduct);
                                } else if (packagingData.selectedFulfillmentType === "WAREHOUSE") {
                                    $scope.warehouseProductsDateWise.push(scmProduct);
                                }
                            }
                            else {
                                // If no packaging data, default to external
                                var specializedRoProduct = specializedRoProductsMap[scmProduct.productId];
                                if(specializedRoProduct) {
                                    specializedRoProduct.selectedFulfillmentType === "EXTERNAL";
                                    specializedRoProduct.packagingUnit = getPackagingUnit(scmProduct.uom);
                                    specializedRoProduct.packagingSize = scmProduct.packagingSize;
                                    specializedRoProduct.packagingQuantity = Math.ceil(specializedRoProduct.finalQuantity / specializedRoProduct.conversionRatio) || 0;
                                    specializedRoProduct.orderingQuantity = specializedRoProduct.packagingQuantity * specializedRoProduct.conversionRatio || 0;
                                    specializedRoProduct.orderingDaysData = scmProduct.orderingDaysData;
                                    specializedRoProduct.remainingDaysData = scmProduct.remainingDaysData;
                                    specializedRoProduct.uom = scmProduct.uom;
                                    specializedRoProduct.finalQuantity = scmProduct.finalQuantity;
                                    $scope.specializedRoProducts.push(specializedRoProduct);
                                }
                            }
                        });

                        console.log("Kitchen Products:", $scope.kitchenProductsDateWise);
                        console.log("Warehouse Products:", $scope.warehouseProductsDateWise);

                        // Store original data for search filtering
                        $scope.originalKitchenProductsDateWise = angular.copy($scope.kitchenProductsDateWise);
                        $scope.originalWarehouseProductsDateWise = angular.copy($scope.warehouseProductsDateWise);

                        // Initialize filtered arrays (maintain references)
                        $scope.filteredKitchenProductsDateWise = $scope.kitchenProductsDateWise;
                        $scope.filteredWarehouseProductsDateWise = $scope.warehouseProductsDateWise;

                        // Create categorized structure with original SCM products
                        $scope.scmPackagingCategoriesDateWise = [
                            {
                                categoryName: "KITCHEN",
                                products: $scope.kitchenProductsDateWise
                            },
                            {
                                categoryName: "WAREHOUSE",
                                products: $scope.warehouseProductsDateWise
                            }
                        ];

                        $alertService.closeAlert();
                        return itemList;
                    }
                );

                $scope.showScmPackagingListDateWise = true;
                $scope.showProductsList = false; // Hide SCM consumption table
            };

            $scope.backToScmConsumptionDateWise = function() {
                if (confirm("Are you sure you want to go back?")) {
                    $scope.scmPackagingProductListDateWise = [];
                    $scope.showScmPackagingListDateWise = false;
                    $scope.showScmProductsDateWise = true;
                }
            };

            $scope.saveComment =function(message){
                $scope.comment =message;
            };

            $scope.updateScmOrderingQtyDateWise = function (product) {
                product.packagingQuantity = parseInt(product.packagingQuantity);
                product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
            };

            $scope.goBackToMenuProductsDateWise = function() {
                $scope.showScmPackagingListDateWise = false;
                $scope.showProductsList = true;
                $scope.originalScmProductsData =[];
                $scope.copyOfTotalStock = angular.copy($scope.originalCopyOfTotalStock);

                // Update modified data when going back
                $scope.modifiedMenuProductsData = JSON.parse(JSON.stringify($scope.productConsumptionList));
            };

            // Get packaging unit based on UOM
            function getPackagingUnit(uom) {
                if (!uom) return 'packets';

                var uomLower = uom.toLowerCase();
                if (uomLower.includes('kg') || uomLower.includes('gm') || uomLower.includes('gram')) {
                    return 'sachets';
                } else if (uomLower.includes('ml') || uomLower.includes('ltr') || uomLower.includes('litre')) {
                    return 'bottles';
                } else if (uomLower.includes('pc') || uomLower.includes('piece')) {
                    return 'packets';
                } else {
                    return 'units';
                }
            }

            // Warehouse toggle function (like suggestive ordering)
            $scope.toggleWarehouseProducts = function() {
                console.log("Warehouse products visibility:", $scope.showWarehouseProducts);
            };

            // Apply expiry logic to SCM products (similar to suggestive ordering)
            function applyExpiryLogicToScmProducts() {
                if (!$scope.hasExpiryLogic || !$scope.expiryDataCheck) {
                    console.log("Expiry logic not applicable");
                    return;
                }

                console.log("Applying expiry logic using getScmProductsAfterExpiry...");

                // Convert SCM products to format expected by getScmProductsAfterExpiry
                var scmProductsConsumptionMap = convertScmProductsToConsumptionMap();
                $scope.makeScmProductsDayWiseConsumption(angular.copy(scmProductsConsumptionMap));
                $scope.makeTotalScmSuggestions(angular.copy(scmProductsConsumptionMap));
                // Call the exact same function as suggestive ordering
                $scope.getScmProductsAfterExpiry(scmProductsConsumptionMap, $scope.dataEntry);

                RecalculateFinalQuantity(scmProductsConsumptionMap);
            }

            function RecalculateFinalQuantity(consumptionMap) {
                $scope.scmProductsDateWiseList.forEach(function(scmProduct) {
                    var total = 0;

                    // Loop through each date in the map
                    Object.keys(consumptionMap).forEach(function(date) {
                        var entries = consumptionMap[date];

                        var matchingEntry = entries.find(function(p) {
                            return p.productId === scmProduct.productId;
                        });

                        if (matchingEntry) {
                            var qty = parseFloat(matchingEntry.orderingQuantity || 0);

                            if ($scope.orderingDays.includes(date)) {
                                scmProduct.orderingDaysData[date] = qty;
                                total += qty;
                            } else if ($scope.remainingDays.includes(date)) {
                                scmProduct.remainingDaysData[date] = qty;
                            }
                        }
                    });

                    // Update final quantity after expiry adjustment
                    scmProduct.finalQuantity = total;
                });
            }


            // Convert SCM products to consumption map format (like suggestive ordering)
            function convertScmProductsToConsumptionMap() {
                var consumptionMap = {};

                // Create date-wise product map
                $scope.remainingDays.forEach(function(date) {
                    var dateString = $scope.dateFormatting(date);
                    consumptionMap[dateString] = [];

                    $scope.scmProductsDateWiseList.forEach(function(scmProduct) {
                        var quantity = parseFloat(scmProduct.remainingDaysData[dateString]) || 0;

                        if (quantity > 0) {
                            consumptionMap[dateString].push({
                                productId: scmProduct.productId,
                                productName: scmProduct.productName,
                                dimension: scmProduct.dimension || "None",
                                averageConsumption: quantity,
                                orderingQuantity: quantity,
                                orderingDay: dateString,
                                suggestedQuantity: quantity
                            });
                        }
                    });
                });

                $scope.orderingDays.forEach(function(date) {
                    var dateString = $scope.dateFormatting(date);
                    consumptionMap[dateString] = [];

                    $scope.scmProductsDateWiseList.forEach(function(scmProduct) {
                        var quantity = parseFloat(scmProduct.orderingDaysData[dateString]) || 0;

                        if (quantity > 0) {
                            consumptionMap[dateString].push({
                                productId: scmProduct.productId,
                                productName: scmProduct.productName,
                                dimension: scmProduct.dimension || "None",
                                averageConsumption: quantity,
                                orderingQuantity: quantity,
                                orderingDay: dateString,
                                suggestedQuantity: quantity
                            });
                        }
                    });
                });

                console.log("Converted SCM products to consumption map:", consumptionMap);
                return consumptionMap;
            }

            $scope.makeTotalScmSuggestions = function (scmSuggestions) {
                $scope.totalScmSuggestions = {};
                angular.forEach(scmSuggestions, function (scmAverages, key) {
                    if ($scope.remainingDaysFinal.indexOf(key) == -1) {
                        angular.forEach(scmAverages, function (scmAverage) {
                            if (appUtil.isEmptyObject($scope.totalScmSuggestions[scmAverage.productId])) {
                                $scope.totalScmSuggestions[scmAverage.productId] = scmAverage.averageConsumption;
                            } else {
                                $scope.totalScmSuggestions[scmAverage.productId] = $scope.totalScmSuggestions[scmAverage.productId] + scmAverage.averageConsumption;
                            }
                        });
                    }
                });
                console.log("Total of Scm Suggestions are : ", $scope.totalScmSuggestions);
            };

            $scope.makeScmProductsDayWiseConsumption = function (scmConsumption) {
                $scope.scmProductsDayWiseConsumption = {};
                $scope.scmProductAdditionData = {};

                var uniqueProductIds  = [];
                angular.forEach(scmConsumption, function (dayListProducts, date) {
                    angular.forEach(dayListProducts, function (prod) {
                        if (uniqueProductIds.indexOf(prod.productId) === -1) {
                            uniqueProductIds.push(prod.productId);
                        }
                        prod.name = $scope.productMap[prod.productId].productName;
                        prod.unitOfMeasure = $scope.productMap[prod.productId].unitOfMeasure;
                        var key = date + "_" + prod.productId;
                        $scope.scmProductsDayWiseConsumption[key] = prod;
                        if(($scope.selectedBrandDetails.id == 3 || $scope.selectedBrandDetails.id == 0 ) && prod.originalConsumption != null && checkOrderingDay(date)){
                           if($scope.scmProductAdditionData[prod.productId]!=null){
                              $scope.scmProductAdditionData[prod.productId] = addAdditionalSuggestionData(prod,$scope.scmProductAdditionData[prod.productId]);
                           }else{
                             $scope.scmProductAdditionData[prod.productId] = getAdditionalSuggestionData(prod);
                           }

                        }
                    });
                });
                angular.forEach(uniqueProductIds, function (uniqueProductId) {
                     angular.forEach($scope.OrderingDaysFinal, function (date) {
                         var key = date + "_" + uniqueProductId;
                         if (!$scope.scmProductsDayWiseConsumption[key]) {
                             var prod = {};
                             prod.name = $scope.productMap[uniqueProductId].productName;
                             prod.productId = uniqueProductId;
                             prod.unitOfMeasure = $scope.productMap[uniqueProductId].unitOfMeasure;
                             $scope.scmProductsDayWiseConsumption[key] = prod;
                         }
                     });
                });

            }

            $scope.getFulfilmentUnits = function () {
                $scope.fulfilmentUnitsMap = {};
                $scope.fulfilmentUnitProductsMap = {};
                $http({
                    method: "GET",
                    url: apiJson.urls.referenceOrderManagement.getFulfilmentUnits,
                    params: {
                        "requestingUnitId": appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    $scope.fulfilmentUnitsMap = response.data;
                    angular.forEach($scope.fulfilmentUnitsMap, function (unitId, fulfilmentType) {
                        $scope.getAvailableProducts(unitId, fulfilmentType);
                    });
                    console.log("fulfilmentUnitsMap is : ", $scope.fulfilmentUnitsMap);
                    console.log("fulfilmentUnitProductsMap is : ", $scope.fulfilmentUnitProductsMap);
                }, function error(response) {
                    console.log("error:" + response);
                    $scope.fulfilmentUnitsMap = {};
                    $scope.fulfilmentUnitProductsMap = {};
                });
            }

            $scope.getAvailableProducts = function (unitId, type) {
                $http({
                    method: "GET",
                    url: apiJson.urls.filter.availableProducts,
                    params: {
                        requestingUnit: appUtil.getCurrentUser().unitId,
                        fulfillmentUnit: unitId
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.fulfilmentUnitProductsMap[type] = response.data;
                        console.log("Products Map = ", $scope.fulfilmentUnitProductsMap);
                    } else {
                        $scope.fulfilmentUnitProductsMap[type] = [-1];
                        $toastService.create("No products Mapped for Unit :: " + unitId);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create("Something went wrong While getting available Products. Please try again!");
                    $scope.fulfilmentUnitProductsMap[type] = [-1];
                });
            };

            $scope.getArray = function (dup, index) {
                var res = [];
                for (var i = 0; i < dup.length; i++) {
                    if (i >= index) {
                        res.push(dup[i]);
                    }
                }
                return res;
            }

            $scope.getBeforeArray = function (dup, index) {
                var res = [];
                for (var i = 0; i < dup.length; i++) {
                    if (i <= index) {
                        res.push(dup[i]);
                    }
                }
                return res;
            }

            $scope.checkProductInBeforeDaysList = function (productId, beforeDaysList) {
                for (var i = 0; i < beforeDaysList.length; i++) {
                    if ($scope.acknowledgedRoDayWiseExpiry[beforeDaysList[i]] !== undefined && $scope.acknowledgedRoDayWiseExpiry[beforeDaysList[i]] != null
                        && $scope.acknowledgedRoDayWiseExpiry[beforeDaysList[i]][productId] !== undefined && $scope.acknowledgedRoDayWiseExpiry[beforeDaysList[i]][productId] != null) {
                        return true;
                    }
                }
                return false;
            };

            function checkForExpiry(product, dayEntry, currentDateIndex, copyOfAllDays, currentDay) {
                var arrayOfLogs = $scope.productWiseFulfilment[product.productId];
                var currentQty = parseFloat(product.orderingQuantity);

                for (var i = 0; i < dayEntry.length; i++) {
                    var expiryProduct = $scope.copyOfTotalStock[product.productId];
                    if (expiryProduct !== undefined && expiryProduct != null) {
                        var dayExpiry = expiryProduct[dayEntry[i]];
                        if (dayExpiry !== undefined && dayExpiry != null) {
                            console.log("day expiry is : ", angular.copy(dayExpiry));
                            console.log("Before updating the expiry quantity is for product id: ", product.productId, angular.copy(expiryProduct));
                            console.log("Date is and current qty is : ", dayEntry[i], angular.copy(currentQty));
                            if (currentQty > 0) {
                                if (currentQty >= dayExpiry) {
                                    var log = "For Fulfilling " + currentDay + " Quantity Of " + parseFloat(currentQty.toFixed(6)) + " Using the (I,T) Quantity " + parseFloat(dayExpiry.toFixed(6)) + " Of " + dayEntry[i] + " Used Completely";
                                    arrayOfLogs.push(log);
                                    currentQty = parseFloat(currentQty.toFixed(6)) - parseFloat(dayExpiry.toFixed(6));
                                    expiryProduct[dayEntry[i]] = 0;
                                    product.orderingQuantity = parseFloat(currentQty.toFixed(6));
                                    log = "After Using (I,T) Quantity Current needed Quantity is : " + product.orderingQuantity;
                                    arrayOfLogs.push(log);
                                    console.log("After updating the expiry quantity is for product id: ", product.productId, angular.copy(expiryProduct));
                                    console.log("After updating the expiry quantity total expiry is :  ", angular.copy($scope.copyOfTotalStock));
                                    if (currentQty === 0) {
                                        break;
                                    }
                                } else {
                                    var log = "For Fulfilling " + currentDay + " Quantity Of " + parseFloat(currentQty.toFixed(6)) + " Using the (I,T) Quantity " + parseFloat(dayExpiry.toFixed(6)) + " Of " + dayEntry[i] + " Fulfilled Completely";
                                    arrayOfLogs.push(log);
                                    expiryProduct[dayEntry[i]] = parseFloat(dayExpiry.toFixed(6)) - parseFloat(currentQty.toFixed(6));
                                    currentQty = 0;
                                    product.orderingQuantity = parseFloat(currentQty.toFixed(6));
                                    log = "After Using (I,T) Quantity Current needed Quantity is : " + product.orderingQuantity;
                                    arrayOfLogs.push(log);
                                    console.log("After updating the expiry quantity is for product id: ", product.productId, angular.copy(expiryProduct));
                                    console.log("After updating the expiry quantity total expiry is :  ", angular.copy($scope.copyOfTotalStock));
                                    break;
                                }
                            } else {
                                break;
                            }
                        }
                    }

                    // checking the ack ro with same expiry date
                    if (currentQty > 0 && $scope.acknowledgedRoDayWiseExpiry !== undefined && $scope.acknowledgedRoDayWiseExpiry != null) {
                        for (var j = 0; j < copyOfAllDays.length; j++) {
                            var acknowledgedRoStock = $scope.acknowledgedRoDayWiseExpiry[copyOfAllDays[j]];
                            if (acknowledgedRoStock !== undefined && acknowledgedRoStock != null &&
                                acknowledgedRoStock[product.productId] !== undefined && acknowledgedRoStock[product.productId] != null) {
                                var acknowledgedStockOfProductForDay = acknowledgedRoStock[product.productId];
                                var dayExpiryRo = acknowledgedStockOfProductForDay[dayEntry[i]];
                                if (dayExpiryRo !== undefined && dayExpiryRo != null) {
                                    console.log("Inside the Acknowledged RO Before updating the expiry quantity is for product id: ", product.productId, angular.copy(acknowledgedStockOfProductForDay));
                                    console.log("Inside the Acknowledged RO Date is and current qty is : ", dayEntry[i], angular.copy(currentQty));
                                    if (currentQty > 0) {
                                        if (currentQty >= dayExpiryRo) {
                                            var log = "For Fulfilling " + currentDay + " Quantity Of " + parseFloat(currentQty.toFixed(6)) + " Using the (A) Quantity " + parseFloat(dayExpiryRo.toFixed(6)) + " Of " + dayEntry[i] + " Which Came On " + copyOfAllDays[j] + " Used Completely";
                                            arrayOfLogs.push(log);
                                            currentQty = parseFloat(currentQty.toFixed(6)) - parseFloat(dayExpiryRo.toFixed(6));
                                            acknowledgedStockOfProductForDay[dayEntry[i]] = 0;
                                            product.orderingQuantity = parseFloat(currentQty.toFixed(6));
                                            log = "After Using (A) Quantity Current needed Quantity is : " + product.orderingQuantity;
                                            arrayOfLogs.push(log);
                                            console.log("Inside the Acknowledged RO After updating the expiry quantity is for product id: ", product.productId, angular.copy(acknowledgedStockOfProductForDay));
                                            console.log("Inside the Acknowledged RO After updating the expiry quantity total expiry is :  ", angular.copy($scope.acknowledgedRoDayWiseExpiry));
                                            if (currentQty === 0) {
                                                break;
                                            }
                                        } else {
                                            var log = "For Fulfilling " + currentDay + " Quantity Of " + parseFloat(currentQty.toFixed(6)) + " Using the (A) Quantity " + parseFloat(dayExpiryRo.toFixed(6)) + " Of " + dayEntry[i] + " Which Came On " + copyOfAllDays[j] + " Fulfilled Completely";
                                            arrayOfLogs.push(log);
                                            acknowledgedStockOfProductForDay[dayEntry[i]] = parseFloat(dayExpiryRo.toFixed(6)) - parseFloat(currentQty.toFixed(6));
                                            currentQty = 0;
                                            product.orderingQuantity = parseFloat(currentQty.toFixed(6));
                                            log = "After Using (A) Quantity Current needed Quantity is : " + product.orderingQuantity;
                                            arrayOfLogs.push(log);
                                            console.log("Inside the Acknowledged RO After updating the expiry quantity is for product id: ", product.productId, angular.copy(acknowledgedStockOfProductForDay));
                                            console.log("Inside the Acknowledged RO After updating the expiry quantity total expiry is :  ", angular.copy($scope.acknowledgedRoDayWiseExpiry));
                                            break;
                                        }
                                    } else {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    if (currentQty === 0) {
                        console.log("Consumed Completely for product id: ", product.productId);
                        break;
                    }
                }
                $scope.productWiseFulfilment[product.productId] = arrayOfLogs;
                return product.orderingQuantity;
            }

            $scope.getProductWiseStock = function () {
                $scope.productWiseStock = {};
                angular.forEach($scope.dayWiseExpiryProduct['inStock'], function (value, key) {
                    var total = 0;
                    angular.forEach($scope.dataEntry, function (entry) {
                        if (value[entry.date] != undefined && value[entry.date] != null) {
                            total = total + value[entry.date];
                            if (appUtil.isEmptyObject($scope.productWiseStock[key])) {
                                $scope.productWiseStock[key] = {};
                                $scope.productWiseStock[key].stockAtHand = {};
                                $scope.productWiseStock[key].stockAtHand["totalStock"] = total;
                                $scope.productWiseStock[key].stockAtHand[entry.date] = value[entry.date];
                            } else {
                                if (appUtil.isEmptyObject($scope.productWiseStock[key].stockAtHand)) {
                                    $scope.productWiseStock[key].stockAtHand = {}
                                    $scope.productWiseStock[key].stockAtHand["totalStock"] = total;
                                    $scope.productWiseStock[key].stockAtHand[entry.date] = value[entry.date];
                                } else {
                                    $scope.productWiseStock[key].stockAtHand["totalStock"] = total;
                                    if (appUtil.isEmptyObject($scope.productWiseStock[key].stockAtHand[entry.date])) {
                                        $scope.productWiseStock[key].stockAtHand[entry.date] = value[entry.date];
                                    } else {
                                        $scope.productWiseStock[key].stockAtHand[entry.date] = $scope.productWiseStock[key].stockAtHand[entry.date] + value[entry.date];
                                    }
                                }
                            }
                        }
                    });
                });

            angular.forEach($scope.dayWiseExpiryProduct['inTransit'], function (value, key) {
                var total = 0;
                angular.forEach($scope.dataEntry, function (entry) {
                    if (value[entry.date] != undefined && value[entry.date] != null) {
                        total = total + value[entry.date];
                        if (appUtil.isEmptyObject($scope.productWiseStock[key])) {
                            $scope.productWiseStock[key] = {};
                            $scope.productWiseStock[key].inTransit = {};
                            $scope.productWiseStock[key].inTransit["totalStock"] = total;
                            $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                        } else {
                            if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit)) {
                                $scope.productWiseStock[key].inTransit = {};
                                $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                            } else {
                                $scope.productWiseStock[key].inTransit["totalStock"] = total;
                                if (appUtil.isEmptyObject($scope.productWiseStock[key].inTransit[entry.date])) {
                                    $scope.productWiseStock[key].inTransit[entry.date] = value[entry.date];
                                } else {
                                    $scope.productWiseStock[key].inTransit[entry.date] = $scope.productWiseStock[key].inTransit[entry.date] + value[entry.date];
                                }
                            }
                        }
                    }
                });
            });

                console.log("Product wise stock of Stock at hand and intransit is : ", $scope.productWiseStock);
                $scope.makeProductWiseExpiryData($scope.productWiseStock);
            };

            $scope.makeProductWiseExpiryData = function (pws) {
                console.log("pws is : ", pws);
                $scope.productWiseExpiryData = {};
                angular.forEach(pws, function (typeOfStock, productId) {
                    $scope.productWiseExpiryData[productId] = {};
                    var currentObj = $scope.productWiseExpiryData[productId];
                    angular.forEach(typeOfStock, function (dateWise, type) {
                        angular.forEach(dateWise, function (quantity, date) {
                            if (!date.startsWith('t')) {
                                var key = date + '_' + type;
                                currentObj[key] = quantity;
                                $scope.productWiseExpiryData[productId] = currentObj;
                            }
                        });
                    });
                });
                console.log("Final ProductWise data is : ", $scope.productWiseExpiryData);
                };

                $scope.showExpiryUsage = function (product) {
                    $scope.selectedProductForLogs = product;
                    $scope.selectedProductLogs = $scope.productWiseFulfilment[product.productId] || [];
                };

                $scope.showAcknowledgedRoStock = function (product) {
                    $scope.selectedProductForAcknowledgedRo = product;
                };

                $scope.getScmProductsAfterExpiry = function (scmProducts, dataEntryData) {
                    $scope.scmProductWiseMap = {};
                    $scope.productWiseFulfilment = {};
                    console.log("entries are ss: ", dataEntryData);

                    for (var i = 0; i < $scope.OrderingDaysFinal.length; i++) {
                        var prods = scmProducts[$scope.OrderingDaysFinal[i]];
                        if (prods !== undefined && prods !== null) {
                            var dup = angular.copy($scope.OrderingDaysFinal);
                            for (var j = 0; j < prods.length; j++) {
                                var arr = $scope.getArray(dup, i);
                                var beforeDaysList = $scope.getBeforeArray(dup, i);
                                if ($scope.productWiseFulfilment[prods[j].productId] === undefined || $scope.productWiseFulfilment[prods[j].productId] === null) {
                                    $scope.productWiseFulfilment[prods[j].productId] = [];
                                }

                                var logs = $scope.productWiseFulfilment[prods[j].productId];
                                var log = "For Day : " + $scope.OrderingDaysFinal[i] + " Needed : " + prods[j].orderingQuantity;
                                logs.push(log);
                                $scope.productWiseFulfilment[prods[j].productId] = logs;

                                if ($scope.copyOfTotalStock[prods[j].productId] !== undefined && $scope.copyOfTotalStock[prods[j].productId] != null ||
                                 ($scope.acknowledgedRoDayWiseExpiry !== undefined && $scope.acknowledgedRoDayWiseExpiry != null && $scope.checkProductInBeforeDaysList(prods[j].productId, beforeDaysList))) {
                                    var finalQty = checkForExpiry(prods[j], arr, i, beforeDaysList, $scope.OrderingDaysFinal[i]);

                                    if ($scope.remainingDaysFinal.indexOf($scope.OrderingDaysFinal[i]) === -1) {
                                        if ($scope.scmProductWiseMap[prods[j].productId] === undefined || $scope.scmProductWiseMap[prods[j].productId] == null) {
                                            $scope.scmProductWiseMap[prods[j].productId] = parseFloat(finalQty);
                                        } else {
                                            var qty = $scope.scmProductWiseMap[prods[j].productId];
                                            $scope.scmProductWiseMap[prods[j].productId] = qty + parseFloat(finalQty);
                                        }
                                    } else {
                                        console.log("Changed data for remaining days qty ..!");
                                    }
                                } else {
                                    if ($scope.remainingDaysFinal.indexOf($scope.OrderingDaysFinal[i]) === -1) {
                                        if ($scope.scmProductWiseMap[prods[j].productId] === undefined || $scope.scmProductWiseMap[prods[j].productId] == null) {
                                            $scope.scmProductWiseMap[prods[j].productId] = parseFloat(prods[j].averageConsumption);
                                        } else {
                                            var qty = $scope.scmProductWiseMap[prods[j].productId];
                                            $scope.scmProductWiseMap[prods[j].productId] = qty + parseFloat(prods[j].averageConsumption);
                                        }
                                    } else {
                                        console.log("Changed data for remaining days qty ..!");
                                    }
                                }
                            }
                        }
                    }
                    console.log("scm Product wise map is : ", $scope.scmProductWiseMap);
                    $scope.getProductWiseStock();
                }

            // getAvailableProducts function (from suggestive ordering)
            $scope.getAvailableProducts = function (unitId, type) {
                $http({
                    method: "GET",
                    url: apiJson.urls.filter.availableProducts,
                    params: {
                        requestingUnit: appUtil.getCurrentUser().unitId,
                        fulfillmentUnit: unitId
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.fulfilmentUnitProductsMap[type] = response.data;
                        console.log("Products Map for", type, ":", $scope.fulfilmentUnitProductsMap[type]);
                    } else {
                        $scope.fulfilmentUnitProductsMap[type] = [-1];
                        console.log("No products mapped for unit:", unitId);
                    }
                }, function error(response) {
                    console.log("Error fetching available products:", response);
                    $scope.fulfilmentUnitProductsMap[type] = [-1];
                });
            };

            $scope.toggleSpecializedRoProducts = function() {
                console.log("Specialized RO products visibility:", $scope.showSpecializedRoProducts);
            };

            // Group products by category and initialize date-wise multipliers
            function groupProductsByCategory() {
                var categoryMap = {};

                // Group products by category
                $scope.productConsumptionList.forEach(function(product) {
                    var category = product.productCategory || "Others";

                    if (!categoryMap[category]) {
                        categoryMap[category] = [];
                    }

                    categoryMap[category].push(product);
                });

                // Convert to array format and initialize date-wise multipliers
                $scope.menuProductCategories = [];
                Object.keys(categoryMap).forEach(function(categoryName) {
                    $scope.menuProductCategories.push({
                        categoryName: categoryName,
                        products: categoryMap[categoryName]
                    });

                    // Initialize multiplier to 100% for each category + date combination
                    $scope.orderingDays.forEach(function(date) {
                        var dateString = $scope.dateFormatting(date);
                        var key = categoryName + "_" + dateString;
                        $scope.categoryDateMultipliers[key] = 100;
                    });
                });

                // Store original categories for filtering
                $scope.originalMenuProductCategories = angular.copy($scope.menuProductCategories);
                $scope.filteredMenuProductCategories = $scope.menuProductCategories;

                // Initialize modifiedMenuProductsData with current state
                $scope.modifiedMenuProductsData = JSON.parse(JSON.stringify($scope.productConsumptionList));

                console.log("Menu Product Categories:", $scope.menuProductCategories);
                console.log("Category Date Multipliers:", $scope.categoryDateMultipliers);
                console.log("Initial Modified Menu Products Data:", $scope.modifiedMenuProductsData);
            }

            $scope.isMultiplierInitialized = {}; // key → true if dropdown is already rendered
            $scope.previousMultipliers = {}; // key = category_date, value = old value

            $scope.storePreviousMultiplier = function(key) {
                $scope.previousMultipliers[key] = $scope.categoryDateMultipliers[key];
            };

            $scope.suppressMultiplierChange = {};

            // Apply multiplier to category products for specific date with confirmation
            $scope.applyCategoryDateMultiplier = function(categoryName, dateString) {
                var key = categoryName + "_" + dateString;
                var multiplier = $scope.categoryDateMultipliers[key];
                var oldValue = $scope.previousMultipliers[key] || 100;

                 // Skip first auto-triggered ng-change
                 if (!$scope.isMultiplierInitialized[key]) {
                    $scope.isMultiplierInitialized[key] = true;
                    return;
                 }

                // If flagged, skip change handling
                if ($scope.suppressMultiplierChange[key]) {
                     $scope.categoryDateMultipliers[key] = $scope.previousMultipliers[key];
                     $scope.suppressMultiplierChange[key] = false;
                     $scope.$applyAsync();
                    return; // skip this round
                }

                // Find the category to get product count
                var category = $scope.menuProductCategories.find(function(cat) {
                    return cat.categoryName === categoryName;
                });

                if (!category) return;

                var productCount = category.products.length;
                var formattedDate = new Date(dateString).toLocaleDateString('en-GB');

                // Show confirmation popup
                var confirmMessage = "This multiplier (" + multiplier + "%) will be applied to all " + productCount +
                                   " products in '" + categoryName + "' category for date " + formattedDate +
                                   ". Do you want to continue?";

                $alertService.confirm("Confirm Multiplier Application", confirmMessage, function(result) {
                    if (result) {
                        // User confirmed - apply the multiplier
                        applyMultiplierToCategory(categoryName, dateString, multiplier);
                    } else {
                        // User cancelled - revert the multiplier back to 100%
                        $scope.suppressMultiplierChange[key] = true;
                        $scope.categoryDateMultipliers[key] = oldValue;
                        $scope.$applyAsync();
                    }
                }, true);
            };

            // Helper function to actually apply the multiplier
            function applyMultiplierToCategory(categoryName, dateString, multiplierPercent) {
                var multiplier = multiplierPercent / 100; // Convert percentage to decimal

                // Find the category
                var category = $scope.menuProductCategories.find(function(cat) {
                    return cat.categoryName === categoryName;
                });

                if (category) {
                    category.products.forEach(function(product) {
                        // Apply multiplier only to the specific date
                        if (product.orderingDaysData[dateString]) {
                            var quantity = Math.max(product.orderingDaysData[dateString].originalQuantity + product.maxBiasQuantity, 0);
                            product.orderingDaysData[dateString].quantity = Math.round(quantity * multiplier);
                        }

                        // Recalculate final quantity
                        $scope.calculateFinalQuantity(product);
                    });

                    // Apply digest cycle once after all products are updated
                    $scope.$applyAsync();
                }

                // Sync changes to modifiedMenuProductsData
                syncUIChangesToModifiedData();
            }

            // Function to sync UI changes to modifiedMenuProductsData
            function syncUIChangesToModifiedData() {
                console.log("Syncing UI changes to modified data...");

                // Update modifiedMenuProductsData with current UI state
                $scope.menuProductCategories.forEach(function(category) {
                    category.products.forEach(function(uiProduct) {
                        // Find corresponding product in modifiedMenuProductsData
                        var modifiedProduct = $scope.modifiedMenuProductsData.find(function(mp) {
                            return mp.productId === uiProduct.productId;
                        });

                        if (modifiedProduct) {
                            // Sync ordering days data
                            if (uiProduct.orderingDaysData) {
                                modifiedProduct.orderingDaysData = angular.copy(uiProduct.orderingDaysData);
                            }

                            // Sync remaining days data
                            if (uiProduct.remainingDaysData) {
                                modifiedProduct.remainingDaysData = angular.copy(uiProduct.remainingDaysData);
                            }

                            // Sync final quantity
                            modifiedProduct.finalQuantity = uiProduct.finalQuantity;
                        }
                    });
                });

                console.log("Modified Menu Products Data synced:", $scope.modifiedMenuProductsData);
            }

            $scope.filterProducts = debounce(function () {
                $rootScope.showSpinner = true;

                $scope.$applyAsync(function () {
                    $scope.debouncedFilterProducts();

                    // Allow rendering to complete before hiding spinner
                    setTimeout(function () {
                        $rootScope.showSpinner = false;
                        $scope.$applyAsync();
                    }, 5000);
                });

            }, 800);

            // Search filter function for Menu Products
            $scope.debouncedFilterProducts = function() {
                if (!$scope.searchText || $scope.searchText.trim() === "") {
                    // If search is empty, show all categories (maintain references, don't copy)
                    $scope.filteredMenuProductCategories = $scope.menuProductCategories;
                } else {
                    var searchTerm = $scope.searchText.toLowerCase().trim();
                    $scope.filteredMenuProductCategories = [];

                    // Filter categories that have matching products (maintain product references)
                    $scope.menuProductCategories.forEach(function(category) {
                        var filteredProducts = category.products.filter(function(product) {
                            return product.productName.toLowerCase().includes(searchTerm);
                        });

                        // Only include category if it has matching products
                        if (filteredProducts.length > 0) {
                            $scope.filteredMenuProductCategories.push({
                                categoryName: category.categoryName,
                                products: filteredProducts  // Keep original product references
                            });
                        }
                    });
                }

                console.log("Menu Search term:", $scope.searchText);
                console.log("Filtered menu categories:", $scope.filteredMenuProductCategories);
            };

            // Search filter function for SCM Products
            $scope.filterScmProducts = debounce(function () {
                // Run inside digest if needed
                $scope.$applyAsync(function () {
                    $scope.debouncedFilterScmProducts();
                });
            }, 800);

            $scope.debouncedFilterScmProducts = function() {
                if (!$scope.scmSearchText || $scope.scmSearchText.trim() === "") {
                    // If search is empty, show all products (maintain references, don't copy)
                    $scope.filteredKitchenProductsDateWise = $scope.kitchenProductsDateWise;
                    $scope.filteredWarehouseProductsDateWise = $scope.warehouseProductsDateWise;
                } else {
                    var searchTerm = $scope.scmSearchText.toLowerCase().trim();

                    // Filter kitchen products (maintain references)
                    $scope.filteredKitchenProductsDateWise = $scope.kitchenProductsDateWise.filter(function(product) {
                        return product.productName.toLowerCase().includes(searchTerm);
                    });

                    // Filter warehouse products (maintain references)
                    $scope.filteredWarehouseProductsDateWise = $scope.warehouseProductsDateWise.filter(function(product) {
                        return product.productName.toLowerCase().includes(searchTerm);
                    });
                }

                console.log("SCM Search term:", $scope.scmSearchText);
                console.log("Filtered kitchen products:", $scope.filteredKitchenProductsDateWise);
                console.log("Filtered warehouse products:", $scope.filteredWarehouseProductsDateWise);
            };

            // Send Reference Order (Similar to Suggestive Ordering)
            $scope.sendReferenceOrder = function (action) {

                var data = createRoObjectMenuConsumption(action);
                var validateOrder = validateFulfillmentMenuConsumption(data);

                if ($scope.fulfillmentDate == null) {
                    $toastService.create('Please fill fulfillment date!');
                    return false;
                } else if (data.referenceOrderScmItems.length == 0) {
                    $toastService.create('Please select at least a few products to order!');
                    return false;
                } else if (validateOrder.length > 0) {
                    $alertService.alert('Products found without fulfillment types', validateOrder.join(","), null, true);
                    return false;
                } else {
                    data.estimationSalesDataRequests = $scope.dataEntry;
                    data.refOrderSource = "MENU_PRODUCTS_CONSUMPTION";
                    console.log("Reference Order Data Request sending to backend:", data);

                    if (confirm("Are you sure you want to create the order?")) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.referenceOrderManagement.referenceOrder,
                            data: data
                        }).then(function success(response) {
                            console.log("Reference Order Response:", response);
                            if (response.data != null && response.data > 0) {
                                $toastService.create("Reference order with id " + response.data + " created successfully!");
                                 $scope.scmPackagingProductListDateWise = [];
                                 $scope.productConsumptionList =[];
                                 $scope.showScmPackagingListDateWise = false;
                                 $scope.showProductsList = false;
                                 $scope.noOfDays=2;
                                 $scope.setDates(appUtil.getDate(2), $scope.noOfDays);
                                 $scope.comment = null;
                                 $scope.searchText = "";
                                 $scope.scmSearchText = "";
                                 $scope.isMultiplierInitialized = {};
                                 $scope.suppressMultiplierChange = {};
                                 $scope.previousMultipliers = {};
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }, function error(response) {
                            console.log("Reference Order Error:", response);
                            $toastService.create("Error creating reference order!");
                        });
                    }
                }
            };

            // Create Reference Order Object (Similar to Suggestive Ordering)
            function createRoObjectMenuConsumption(status) {
                return {
                    id: null,
                    generationTime: null,
                    initiationTime: null,
                    lastUpdateTime: null,
                    requestUnit: getRequestUnit(),
                    generatedBy: appUtil.createGeneratedBy(),
                    fulfillmentUnit: null,
                    fulfillmentDate: new Date($scope.fulfillmentDate),
                    status: status,
                    comment: $scope.comment || "Created from Menu Products Consumption",
                    referenceOrderMenuItems: getMenuItemsConsumption(),
                    referenceOrderScmItems: getScmItemsConsumption(),
                    numberOfDays: $scope.noOfDays,
                    raiseBy: $scope.raiseBy,
                    orderEvent: null,
                    refreshDate: new Date(),
                    brand: $scope.selectedBrandDetails.brandName,
                    expiryUsageLogs: $scope.productWiseFulfilment
                };
            }

            function getRequestUnit() {
                return {
                    id: appUtil.getCurrentUser().unitId,
                    name: appUtil.getUnitData().name,
                    code: null
                };
            }

            function getMenuItemsConsumption() {
                var menuItems = [];

                $scope.productConsumptionList.forEach(function(product) {

                    // Convert orderingDaysData and remainingDaysData to key-value maps
                    var dateOrderings = {};
                    var multiplierMap = {};
                    if (product.orderingDaysData) {
                        Object.keys(product.orderingDaysData).forEach(function(date) {
                            dateOrderings[date] = parseSafeBigDecimal(product.orderingDaysData[date].quantity);
                            var key = product.productCategory + "_" + date;
                            multiplierMap[date] = $scope.categoryDateMultipliers[key];
                        });
                    }

                    var dateRemaining = {};
                    if (product.remainingDaysData) {
                        Object.keys(product.remainingDaysData).forEach(function(date) {
                            dateRemaining[date] = parseSafeBigDecimal(product.remainingDaysData[date]);
                        });
                    }

                    var originalRemainingDays = {};
                    var originalOrderingDays = {};
                    var originalProduct = $scope.originalMenuProductsData.find(function(p) {
                        return p.productId === product.productId;
                    });
                    if (originalProduct && originalProduct.remainingDaysData) {
                        Object.keys(originalProduct.remainingDaysData).forEach(function(date) {
                            originalRemainingDays[date] = parseSafeBigDecimal(originalProduct.remainingDaysData[date]);
                        });
                    }
                    product.originalRemainingDays = originalRemainingDays;

                    if (originalProduct && originalProduct.orderingDaysData) {
                        Object.keys(originalProduct.orderingDaysData).forEach(function(date) {
                            originalOrderingDays[date] = parseSafeBigDecimal(originalProduct.orderingDaysData[date].quantity);
                        });
                    }
                    product.originalOrderingDays = originalOrderingDays;
                    product.originalFinalQuantity = originalProduct ? originalProduct.finalQuantity : 0;

                    menuItems.push({
                        id: null,
                        productId: product.productId,
                        productName: product.productName || "",
                        dimension: product.dimension || null,
                        requestedQuantity: parseSafeBigDecimal(product.finalQuantity),
                        requestedAbsoluteQuantity: parseSafeBigDecimal(product.finalQuantity),  // You can change this if needed
                        transferredQuantity: null,
                        receivedQuantity: null,
                        quantity: parseSafeBigDecimal(product.originalFinalQuantity),
                        saleQuantity: null,
                        dineInQuantity: null,
                        deliveryQuantity: null,
                        takeawayQuantity: null,
                        variants: null, // or fill if needed
                        dateOrderings: dateOrderings,
                        dateRemaining: dateRemaining,
                        multiplierMap: multiplierMap,
                        originalDateRemaining: originalRemainingDays,
                        originalDateOrderings: originalOrderingDays,
                        originalOrderingQuantity: parseSafeBigDecimal(product.originalOrderingQuantity),
                        originalSaleQuantity: null
                    });
                });

                return menuItems;
            }

            // Helper function to safely parse decimal fields
            function parseSafeBigDecimal(value) {
                var num = parseFloat(value);
                return isNaN(num) ? 0 : parseFloat(num.toFixed(6));
            }

            // Get SCM Items for Reference Order
            function getScmItemsConsumption() {
                var scmItems = [];

                var allProducts = $scope.kitchenProductsDateWise.concat($scope.warehouseProductsDateWise);

                allProducts.forEach(function (product) {

                    var dateOrderings = {};
                    if (product.orderingDaysData) {
                        Object.keys(product.orderingDaysData).forEach(function(date) {
                            dateOrderings[date] = parseSafeBigDecimal(product.orderingDaysData[date]);
                        });
                    }
                    product.dateOrderings = dateOrderings;

                    var originalRemainingDays = {};
                    var originalOrderingDays = {};
                    var originalProduct = $scope.originalScmProductsData.find(function(p) {
                        return p.productId === product.productId;
                    });

                    if (originalProduct && originalProduct.remainingDaysData) {
                        Object.keys(originalProduct.remainingDaysData).forEach(function(date) {
                            originalRemainingDays[date] = parseSafeBigDecimal(originalProduct.remainingDaysData[date]);
                        });
                    }
                    product.originalRemainingDays = originalRemainingDays;

                    if (originalProduct && originalProduct.orderingDaysData) {
                        Object.keys(originalProduct.orderingDaysData).forEach(function(date) {
                            originalOrderingDays[date] = parseSafeBigDecimal(originalProduct.orderingDaysData[date]);
                        });
                    }
                    product.originalOrderingDays = originalOrderingDays;

                    var dateRemaining = {};
                    if (product.remainingDaysData) {
                        Object.keys(product.remainingDaysData).forEach(function(date) {
                            dateRemaining[date] = parseSafeBigDecimal(product.remainingDaysData[date]);
                        });
                    }
                    product.dateRemaining = dateRemaining;

                    product.originalFinalQuantity = originalProduct ? originalProduct.finalQuantity : 0;

                    var packagingQuantity = Math.ceil(product.originalFinalQuantity/product.conversionRatio);
                    if(product.conversionRatio>=4 && product.originalFinalQuantity>product.conversionRatio){
                        packagingQuantity = roundQuantity(product.originalFinalQuantity/product.conversionRatio);
                    };
                    product.suggestedQuantity = packagingQuantity * product.conversionRatio;

                    if (product.finalQuantity > 0) {
                        if ($scope.showWarehouseProducts) {
                            // Allow both Kitchen and Warehouse fulfillment
                            if (product.selectedFulfillmentType === 'KITCHEN' || product.selectedFulfillmentType === 'WAREHOUSE') {
                                scmItems.push(makeScmProductObject(product));
                            }
                        } else {
                            // Only allow Kitchen if warehouse ordering not enabled
                            if (product.selectedFulfillmentType === 'KITCHEN') {
                                scmItems.push(makeScmProductObject(product));
                            }
                        }
                    }
                });

                console.log("SCM Items for Reference Order:", scmItems);
                return scmItems;
            }

            function makeScmProductObject(product) {
                return {
                    id: null,
                    productId: product.productId,
                    productName: product.productName,
                    suggestedQuantity: product.suggestedQuantity,
                    requestedQuantity: parseFloat(parseFloat(product.orderingQuantity).toFixed(6)),
                    requestedAbsoluteQuantity: parseFloat(parseFloat(product.orderingQuantity).toFixed(6)),
                    suggestedQuantityBeforeMoq: parseFloat(parseFloat(product.originalFinalQuantity).toFixed(6)),
                    transferredQuantity: null,
                    receivedQuantity: null,
                    fulfillmentType: product.selectedFulfillmentType,
                    unitOfMeasure: product.unitOfMeasure || product.uom,
                    predictedQuantity: parseFloat(parseFloat(product.predictedQuantity).toFixed(6)),
                    dateOrderings: product.dateOrderings,
                    originalDateRemaining: product.originalRemainingDays,
                    originalDateOrderings: product.originalOrderingDays,
                    originalFinalQuantity: product.originalFinalQuantity,
                    dateRemaining: product.dateRemaining,
                    reason: !appUtil.isEmptyObject(product.reason) ? product.reason : null,
                    packagingId: product.packagingId
                };
            }

            // Validate Fulfillment Types
            function validateFulfillmentMenuConsumption(data) {
                var invalidProducts = [];

                data.referenceOrderScmItems.forEach(function(item) {
                    if (!item.fulfillmentType || item.fulfillmentType === "") {
                        invalidProducts.push(item.productName);
                    }
                });

                return invalidProducts;
            }

        }]);
