/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 27-04-2016.
 */
'use strict';

angular.module('scmApp')
    .controller('menuCtrl', ['$rootScope','authService','$state','$scope','apiJson','appUtil','metaDataService',
        '$cookieStore','$http','$location','$window','$timeout',
        function($rootScope,authService,$state,$scope,apiJson,appUtil,metaDataService,$cookieStore,$http,$location,$window,$timeout){
            $scope.activeMenu = $rootScope.activeMenu;
            // Suggestive Ordering Strategy Flag and Object
            $scope.isSuggestiveOrderingStrategyAvailable = false;
            $scope.suggestiveOrderingStrategy = null;
            $scope.activateLoader = function(destination) {
                if($scope.activeMenu!==destination) {
                    $rootScope.showSpinner = true;
                }
                console.log("activateMenu:", $scope.activeMenu, "| destination:", destination);
                $scope.activeMenu = destination;
                setTimeout(function () {
                    $rootScope.showSpinner = false;
                },8000);

            }
            $scope.hasPermissions = function(){
        	    if($rootScope.aclData!=null){
                    return Object.keys($rootScope.aclData.menu).length > 0;
                }else {
        	        return false;
                }

            };

            $scope.hasAccess = function(key){
                if($rootScope.aclData!=null){
                    return Object.keys($rootScope.aclData.subMenu).length > 0 && $rootScope.aclData.subMenu[key] != undefined;
                }else {
                    return false;
                }
            };

            // Function to check suggestive ordering strategy availability and get strategy object
            $scope.checkSuggestiveOrderingStrategyAvailable = function() {
                console.log("Checking suggestive ordering strategy availability...");

                $http({
                    method: "GET",
                    url: apiJson.urls.referenceOrderManagement.checkSuggestiveOrderingStrategyAvailable,
                    params: {
                        unitId: appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    if (response.data !== null && response.data !== undefined && response.data.id != null) {
                        // Store the complete strategy object
                        $scope.suggestiveOrderingStrategy = response.data;
                        $scope.isSuggestiveOrderingStrategyAvailable = true;

                        console.log("Suggestive Ordering Strategy Object:", $scope.suggestiveOrderingStrategy);
                        console.log("Strategy Available:", $scope.isSuggestiveOrderingStrategyAvailable);

                        // Store in rootScope for access across controllers
                        $rootScope.suggestiveOrderingStrategy = $scope.suggestiveOrderingStrategy;
                        $rootScope.isSuggestiveOrderingStrategyAvailable = $scope.isSuggestiveOrderingStrategyAvailable;

                    } else {
                        // Null response means no strategy available
                        $scope.isSuggestiveOrderingStrategyAvailable = false;
                        $scope.suggestiveOrderingStrategy = null;
                        $rootScope.suggestiveOrderingStrategy = null;
                        $rootScope.isSuggestiveOrderingStrategyAvailable = false;
                        console.log("Suggestive Ordering Not Available - Strategy object is null");
                    }
                }, function error(response) {
                    console.log("Error checking suggestive ordering strategy:", response);
                    $scope.isSuggestiveOrderingStrategyAvailable = false;
                    $scope.suggestiveOrderingStrategy = null;
                    $rootScope.suggestiveOrderingStrategy = null;
                    $rootScope.isSuggestiveOrderingStrategyAvailable = false;
                });
            };

            $scope.hasAccessTo_SCM_HOD = function(){
                var hasAccess = $rootScope.aclData &&
                                $rootScope.aclData.action &&
                                $rootScope.aclData.action.SCM_HOD !== undefined;
                var filterValue = localStorage.getItem('supScmAdmnFilter');
                $rootScope.supScmAdmnFilter = hasAccess ? (filterValue === "true") : true;
                return hasAccess;
            };

            $scope.isExternalCompany = function() {
                if (appUtil.getUnitData() != null &&
                    appUtil.getUnitData().company != null &&
                    appUtil.getUnitData().company.id != null) {

                    return !(appUtil.getUnitData().company.id == 1000 ||
                             appUtil.getUnitData().company.id == 1005);
                }
                return true; // default external
            };

            $scope.hasAccessToMenu = function (key){
                if($rootScope.aclData!=null){
                    return Object.keys($rootScope.aclData.menu).length > 0 && $rootScope.aclData.menu[key] != undefined;
                }else {
                    return false;
                }
            }

            $scope.init = function(){
                $rootScope.activeMenu = appUtil.checkEmpty($rootScope.activeMenu) ? $rootScope.activeMenu : "po";
                $(".button-collapse").sideNav();
                $(".collapsible").collapsible();
                $scope.showToAdmin = true; //appUtil.checkPermission('scm-service.*');
                $scope.isProductManager = true; //appUtil.checkPermission('scm-service.product-management.*');
                $scope.hasReportPrivilege = true; //appUtil.checkPermission("scm-service.report-management.*");
                $scope.hasSKUManagementPrivilege = true; //appUtil.checkPermission("scm-service.sku-price-management.view.*");
                $scope.hasSKUAdminPrivilege = true; //appUtil.checkPermission("scm-service.sku-price-management.approve.*");
                $scope.isVendorManager = true; //appUtil.checkPermission("scm-service.vendor-management.*");
                $scope.isWHOrKitchen  = appUtil.isWarehouseOrKitchen();
                $scope.isKitchen = appUtil.isKitchen();
                $scope.hasApprovePermissions = true; //appUtil.checkPermission("scm-service.purchase-order-management.approve-po");
                $scope.canCreatePO = true; //appUtil.checkPermission("scm-service.purchase-order-management.create-po");
                if(appUtil.getLoginType() == null || appUtil.getLoginType() == undefined){
                    $state.go('login');
                    return;
                } else if(appUtil.getLoginType() == "sumo" && $rootScope.loginType == undefined){
                    $state.go('login');
                    return;
                } else if ($rootScope.loginType == undefined){
                    $rootScope.loginType = appUtil.getLoginType();
                }
                $scope.hasAccessTo_SCM_HOD();
                $scope.isExternalCompany();
                $scope.setUnitName();
                $scope.userId = appUtil.getCurrentUser().userId;
                $scope.userName = appUtil.getCurrentUser().user.name;
                $scope.unitId = appUtil.getCurrentUser().unitId;
                // Call the function on menu load (login time)
                $scope.checkSuggestiveOrderingStrategyAvailable();
                $scope.showVendorOrdering = appUtil.isWarehouseOrKitchen();
                console.log("menu reached");
                fetchOutletsForEmployee();
                if($rootScope.loginType=="sumo"){
                    $scope.showUnitLogin = true;
                    if ($rootScope.previousState.name == "metadata") {
                        if($scope.hasPermissions()){
                            if (appUtil.isWarehouseOrKitchen() && $scope.hasAccess('TRNPP')) {
                                $state.go('menu.prodPlanning');
                                $scope.activeMenu = "prodPlanning";
                                metaDataService.getInitiatedDayCloseEvent(appUtil.getUnitData().id);
                            } else if(!appUtil.isWarehouseOrKitchen() && $scope.hasAccess('TRNAO')) {
                                $state.go('menu.acknowledgeRO');
                                $scope.activeMenu = "acknowledgeRO";
                                $rootScope.restrictAll = false;
                                metaDataService.getRegularOrderingEvents(appUtil.getUnitData().id);
                            } else if($scope.hasAccessToMenu('SSG')){
                                $state.go('menu.b2bOutWardRegister')
                                $scope.activeMenu = "b2bOutWardRegister";
                            }else{
                                $state.go('menu.supportLink');
                            }
                        }else{
                            $state.go('menu.supportLink');
                        }
                    }
                }else{
                    if($scope.hasPermissions() && $scope.hasAccess("APRSO")){
                                            $scope.showUnitLogin = false;
                                            $state.go("menu.approveSO",{createdSO:null, viewSO:false});
                                            $scope.activeMenu = "viewSOFalse";
                     }else if($scope.hasPermissions() && $scope.hasAccess("VWSO")){
                        $scope.showUnitLogin = false;
                        $state.go("menu.viewSO",{createdSO:null, viewSO:true});
                        $scope.activeMenu="viewSO";
                    }else{
                        $state.go('menu.supportLink');
                    }
                }
            };

            $scope.goToNewRegularOrdering = function (events) {
                $state.go("menu.refOrderCreateV1", {orderingEvents : events});
            }

            function fetchOutletsForEmployee() {
                $scope.userUnitArray = [];
                $scope.responseArray = {};
                    $http({
                        method: 'POST',
                        url: apiJson.urls.unitManagement.userUnits,
                        data: {
                            employeeId: $scope.userId,
                            onlyActive: true
                        }
                    }).then(function success(response) {
                        $scope.responseArray = response.data;
                        $scope.responseArray.forEach(function (unit) {
                            if ((unit.category == "CAFE" || unit.category == "KITCHEN" || unit.category == "WAREHOUSE") && unit.status == "ACTIVE") {
                                $scope.userUnitArray.push(unit);
                            }
                        });

                    }, function error(response) {
                        console.log("Encountered an error",response);
                    });
            }

            $scope.showNavMenu = function() {
                if(document.getElementsByClassName("mobileViewMenu")[0].classList.contains("collapsedMenu")){
                    document.getElementsByClassName("mobileViewMenu")[0].classList.remove("collapsedMenu");
                }
                else{
                    document.getElementsByClassName("mobileViewMenu")[0].classList.add("collapsedMenu");
                }

            }

            $scope.setUnitName = function(){
                var data = appUtil.getUnitData();
                if(!appUtil.isEmptyObject(data) && $rootScope.loginType == "sumo"){
                    $scope.unitName = data.name + "("+data.company.shortCode+")";
                    $scope.outlet = data;
                }
            };

            $scope.relogin = function(outlet){
                $scope.outlet = outlet;
                var userObj = {
                    unitId : $scope.outlet.id,
                    applicationName: "SERVICE_ORDER"
                }

                if($rootScope.loginType=="sumo"){
                    userObj.applicationName="SCM_SERVICE";
                }
                $rootScope.switchUnitLogin(userObj);
            }
            // call this function whenever there is a need to refresh metadata definitions

            $rootScope.logout = function(reload){
                $rootScope.resetLocalStorage();
                window.location = window.location.href+'?eraseCache=true';
                authService.setAuthorization(null);
                window.location = window.location.href.split("#")[0] +"#/login";
                if(reload){
                	$window.location.reload();
                }
            };

            $scope.toggleCompanyFilter = function () {
                if($scope.hasAccessTo_SCM_HOD()) {
                    $rootScope.supScmAdmnFilter = !$rootScope.supScmAdmnFilter;
                    localStorage.setItem('supScmAdmnFilter', $rootScope.supScmAdmnFilter.toString());
                    $state.reload(); // Apply filter changes
                }
            }


        }
    ]
);
