-- queries related to stock out percentage calculations

CREATE TABLE KETTLE_STOCK_OUT_PERCENTAGE_DATA (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID INT NOT NULL,
    BUSINESS_DATE DATE NOT NULL,
    BRAND_ID INT,
    INVENTORY_TRACK_LEVEL VARCHAR(255),
    PRODUCT_COUNT INT,
    CAFE_OPENING TIME,
    CAFE_CLOSING TIME,
    CAFE_OPERATIONAL VARCHAR(10),
    PRODUCT_OPERATIONAL_TIME_IN_MIN INT,
    TOTAL_OPERATION_TIME_IN_MIN INT,
    TOTAL_DOWN_TIME_IN_MIN INT,
    STOCK_OUT_PERCENTAGE DECIMAL(19, 4)
);

ALTER TABLE KETTLE_STOCK_OUT_DATE_WISE_DATA
ADD COLUMN BRAND_ID INT,
ADD COLUMN INVENTORY_TRACK_LEVEL VARCHAR(255),
ADD COLUMN TOTAL_STOCK_OUT_TIME BIGINT;

ALTER TABLE SCHEDULER_STATUS_DATA
ADD COLUMN SCHEDULER_TYPE VARCHAR(255),
ADD COLUMN MESSAGE TEXT;

CREATE TABLE KETTLE_PRODUCT_DATA_CLONE (
    ID INT NOT NULL AUTO_INCREMENT,
    PRODUCT_ID INT NOT NULL,
    BRAND_ID INT,
    INVENTORY_TRACK_LEVEL VARCHAR(255),
    PRIMARY KEY (ID)
);

CREATE TABLE KETTLE_UNIT_DETAIL_DATA_CLONE (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID INT NOT NULL,
    CAFE_OPENING TIME,
    CAFE_CLOSING TIME,
    CAFE_OPERATIONAL VARCHAR(10),
    BRAND_ID INT,
    INVENTORY_LEVEL VARCHAR(255),
    TOTAL_PRODUCTS INT
);

